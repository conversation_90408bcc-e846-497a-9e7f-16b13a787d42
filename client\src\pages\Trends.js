import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useSocket } from '../contexts/SocketContext';
import axios from 'axios';
import toast from 'react-hot-toast';
import InfiniteScroll from 'react-infinite-scroll-component';
import {
  HashtagIcon,
  TrendingUpIcon,
  UserGroupIcon,
  FireIcon,
  ClockIcon,
  MapPinIcon,
  GlobeAltIcon,
  ChevronRightIcon,
  EllipsisHorizontalIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import TweetCard from '../components/Tweet/TweetCard';
import UserCard from '../components/User/UserCard';
import moment from 'moment';

function Trends() {
  const { user } = useAuth();
  const { socket } = useSocket();
  const navigate = useNavigate();
  
  const [activeTab, setActiveTab] = useState('trending'); // 'trending', 'hashtags', 'users'
  const [location, setLocation] = useState('global'); // 'global', 'local'
  const [timeframe, setTimeframe] = useState('24h'); // '1h', '24h', '7d'
  
  // Trending data
  const [trendingTopics, setTrendingTopics] = useState([]);
  const [trendingTweets, setTrendingTweets] = useState([]);
  const [suggestedUsers, setSuggestedUsers] = useState([]);
  const [hashtags, setHashtags] = useState([]);
  
  // Loading states
  const [loading, setLoading] = useState(true);
  const [tweetsLoading, setTweetsLoading] = useState(false);
  const [usersLoading, setUsersLoading] = useState(false);
  
  // Pagination
  const [tweetsPage, setTweetsPage] = useState(1);
  const [usersPage, setUsersPage] = useState(1);
  const [hasMoreTweets, setHasMoreTweets] = useState(true);
  const [hasMoreUsers, setHasMoreUsers] = useState(true);
  
  // Following states
  const [followingUsers, setFollowingUsers] = useState(new Set());
  const [hiddenTopics, setHiddenTopics] = useState(new Set());
  const [dropdownOpen, setDropdownOpen] = useState(null);

  useEffect(() => {
    fetchTrendsData();
  }, [location, timeframe]);

  useEffect(() => {
    if (activeTab === 'trending' && trendingTweets.length === 0) {
      fetchTrendingTweets();
    } else if (activeTab === 'users' && suggestedUsers.length === 0) {
      fetchSuggestedUsers();
    }
  }, [activeTab]);

  useEffect(() => {
    if (socket) {
      socket.on('trendingUpdated', handleTrendingUpdated);
      socket.on('tweetDeleted', handleTweetDeleted);
      socket.on('tweetUpdated', handleTweetUpdated);
      
      return () => {
        socket.off('trendingUpdated', handleTrendingUpdated);
        socket.off('tweetDeleted', handleTweetDeleted);
        socket.off('tweetUpdated', handleTweetUpdated);
      };
    }
  }, [socket]);

  const fetchTrendsData = async () => {
    try {
      setLoading(true);
      const [topicsResponse, hashtagsResponse] = await Promise.all([
        axios.get('/api/trends/topics', {
          params: { location, timeframe }
        }),
        axios.get('/api/trends/hashtags', {
          params: { location, timeframe }
        })
      ]);
      
      setTrendingTopics(topicsResponse.data.topics);
      setHashtags(hashtagsResponse.data.hashtags);
    } catch (error) {
      console.error('Error fetching trends data:', error);
      toast.error('获取趋势数据失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchTrendingTweets = async (page = 1) => {
    if (tweetsLoading) return;
    
    try {
      setTweetsLoading(true);
      const response = await axios.get('/api/trends/tweets', {
        params: { page, limit: 10, location, timeframe }
      });
      
      const newTweets = response.data.tweets;
      if (page === 1) {
        setTrendingTweets(newTweets);
      } else {
        setTrendingTweets(prev => [...prev, ...newTweets]);
      }
      
      setHasMoreTweets(newTweets.length === 10);
      setTweetsPage(page + 1);
    } catch (error) {
      console.error('Error fetching trending tweets:', error);
      toast.error('获取热门推文失败');
    } finally {
      setTweetsLoading(false);
    }
  };

  const fetchSuggestedUsers = async (page = 1) => {
    if (usersLoading) return;
    
    try {
      setUsersLoading(true);
      const response = await axios.get('/api/users/suggested', {
        params: { page, limit: 20 }
      });
      
      const newUsers = response.data.users;
      if (page === 1) {
        setSuggestedUsers(newUsers);
      } else {
        setSuggestedUsers(prev => [...prev, ...newUsers]);
      }
      
      setHasMoreUsers(newUsers.length === 20);
      setUsersPage(page + 1);
    } catch (error) {
      console.error('Error fetching suggested users:', error);
      toast.error('获取推荐用户失败');
    } finally {
      setUsersLoading(false);
    }
  };

  const handleFollowUser = async (userId) => {
    try {
      setFollowingUsers(prev => new Set([...prev, userId]));
      
      const user = suggestedUsers.find(u => u._id === userId);
      if (user.isFollowing) {
        await axios.delete(`/api/users/${userId}/follow`);
        setSuggestedUsers(prev => prev.map(u => 
          u._id === userId ? { ...u, isFollowing: false, followersCount: u.followersCount - 1 } : u
        ));
        toast.success('已取消关注');
      } else {
        await axios.post(`/api/users/${userId}/follow`);
        setSuggestedUsers(prev => prev.map(u => 
          u._id === userId ? { ...u, isFollowing: true, followersCount: u.followersCount + 1 } : u
        ));
        toast.success('关注成功');
      }
    } catch (error) {
      console.error('Error following user:', error);
      toast.error('操作失败');
    } finally {
      setFollowingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(userId);
        return newSet;
      });
    }
  };

  const handleHideTopic = (topicId) => {
    setHiddenTopics(prev => new Set([...prev, topicId]));
    setTrendingTopics(prev => prev.filter(topic => topic._id !== topicId));
    toast.success('已隐藏此话题');
  };

  const handleTrendingUpdated = (data) => {
    if (data.type === 'topics') {
      setTrendingTopics(data.topics);
    } else if (data.type === 'hashtags') {
      setHashtags(data.hashtags);
    }
  };

  const handleTweetDeleted = (tweetId) => {
    setTrendingTweets(prev => prev.filter(tweet => tweet._id !== tweetId));
  };

  const handleTweetUpdated = (updatedTweet) => {
    setTrendingTweets(prev => prev.map(tweet => 
      tweet._id === updatedTweet._id ? updatedTweet : tweet
    ));
  };

  const formatTweetCount = (count) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white dark:bg-dark-100">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white/80 dark:bg-dark-100/80 backdrop-blur-md border-b border-gray-200 dark:border-dark-300">
        <div className="max-w-2xl mx-auto px-4 py-3">
          <h1 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
            趋势
          </h1>
          
          {/* Filters */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <select
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                className="text-sm border border-gray-300 dark:border-dark-300 rounded-lg px-3 py-1.5 bg-white dark:bg-dark-200 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="global">全球</option>
                <option value="local">本地</option>
              </select>
              {location === 'global' ? (
                <GlobeAltIcon className="h-4 w-4 text-gray-400" />
              ) : (
                <MapPinIcon className="h-4 w-4 text-gray-400" />
              )}
            </div>
            
            <div className="flex items-center space-x-2">
              <select
                value={timeframe}
                onChange={(e) => setTimeframe(e.target.value)}
                className="text-sm border border-gray-300 dark:border-dark-300 rounded-lg px-3 py-1.5 bg-white dark:bg-dark-200 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="1h">过去1小时</option>
                <option value="24h">过去24小时</option>
                <option value="7d">过去7天</option>
              </select>
              <ClockIcon className="h-4 w-4 text-gray-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="max-w-2xl mx-auto">
        <div className="flex border-b border-gray-200 dark:border-dark-300">
          <button
            onClick={() => setActiveTab('trending')}
            className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'trending'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            热门推文
          </button>
          <button
            onClick={() => setActiveTab('hashtags')}
            className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'hashtags'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            话题标签
          </button>
          <button
            onClick={() => setActiveTab('users')}
            className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'users'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            推荐关注
          </button>
        </div>

        {/* Content */}
        {activeTab === 'trending' && (
          <div>
            {/* Trending Topics */}
            {trendingTopics.length > 0 && (
              <div className="border-b border-gray-200 dark:border-dark-300">
                <div className="p-4">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center space-x-2">
                    <TrendingUpIcon className="h-5 w-5 text-orange-500" />
                    <span>热门话题</span>
                  </h2>
                  <div className="space-y-3">
                    {trendingTopics.slice(0, 5).map((topic, index) => (
                      <div key={topic._id} className="flex items-center justify-between group">
                        <Link
                          to={`/search?q=${encodeURIComponent(topic.keyword)}`}
                          className="flex-1 flex items-center space-x-3 hover:bg-gray-50 dark:hover:bg-dark-200 rounded-lg p-2 -m-2 transition-colors"
                        >
                          <div className="flex items-center justify-center w-8 h-8 bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 rounded-full text-sm font-bold">
                            {index + 1}
                          </div>
                          <div className="flex-1">
                            <p className="font-medium text-gray-900 dark:text-white">
                              {topic.keyword}
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {formatTweetCount(topic.tweetCount)} 条推文
                            </p>
                          </div>
                          <ChevronRightIcon className="h-4 w-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300" />
                        </Link>
                        
                        <div className="relative">
                          <button
                            onClick={() => setDropdownOpen(dropdownOpen === topic._id ? null : topic._id)}
                            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-300 rounded-full transition-colors opacity-0 group-hover:opacity-100"
                          >
                            <EllipsisHorizontalIcon className="h-4 w-4" />
                          </button>
                          
                          {dropdownOpen === topic._id && (
                            <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-dark-200 rounded-lg shadow-lg border border-gray-200 dark:border-dark-300 py-1 z-10">
                              <button
                                onClick={() => {
                                  handleHideTopic(topic._id);
                                  setDropdownOpen(null);
                                }}
                                className="flex items-center space-x-2 w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-300 transition-colors"
                              >
                                <EyeSlashIcon className="h-4 w-4" />
                                <span>隐藏此话题</span>
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
            
            {/* Trending Tweets */}
            <div>
              {trendingTweets.length === 0 && !tweetsLoading ? (
                <div className="text-center py-12">
                  <div className="text-gray-400 dark:text-gray-500 mb-4">
                    <FireIcon className="mx-auto h-12 w-12" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    暂无热门推文
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400">
                    当前时间段内没有热门推文。
                  </p>
                </div>
              ) : (
                <InfiniteScroll
                  dataLength={trendingTweets.length}
                  next={() => fetchTrendingTweets(tweetsPage)}
                  hasMore={hasMoreTweets}
                  loader={
                    <div className="flex justify-center py-4">
                      <LoadingSpinner size="md" />
                    </div>
                  }
                  endMessage={
                    trendingTweets.length > 0 && (
                      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                        没有更多推文了
                      </div>
                    )
                  }
                >
                  <div className="divide-y divide-gray-200 dark:divide-dark-300">
                    {trendingTweets.map((tweet) => (
                      <TweetCard
                        key={tweet._id}
                        tweet={tweet}
                        onUpdate={(updatedTweet) => handleTweetUpdated(updatedTweet)}
                        onDelete={() => handleTweetDeleted(tweet._id)}
                        showTrendingBadge
                      />
                    ))}
                  </div>
                </InfiniteScroll>
              )}
            </div>
          </div>
        )}

        {activeTab === 'hashtags' && (
          <div>
            {hashtags.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 dark:text-gray-500 mb-4">
                  <HashtagIcon className="mx-auto h-12 w-12" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  暂无热门标签
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  当前时间段内没有热门话题标签。
                </p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200 dark:divide-dark-300">
                {hashtags.map((hashtag, index) => (
                  <Link
                    key={hashtag._id}
                    to={`/search?q=${encodeURIComponent('#' + hashtag.tag)}`}
                    className="block p-4 hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-full text-sm font-bold">
                          {index + 1}
                        </div>
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            #{hashtag.tag}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {formatTweetCount(hashtag.count)} 条推文
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                        {hashtag.trend > 0 ? (
                          <>
                            <TrendingUpIcon className="h-4 w-4 text-green-500" />
                            <span className="text-green-500">+{hashtag.trend}%</span>
                          </>
                        ) : hashtag.trend < 0 ? (
                          <>
                            <TrendingUpIcon className="h-4 w-4 text-red-500 transform rotate-180" />
                            <span className="text-red-500">{hashtag.trend}%</span>
                          </>
                        ) : (
                          <span>持平</span>
                        )}
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'users' && (
          <div>
            {suggestedUsers.length === 0 && !usersLoading ? (
              <div className="text-center py-12">
                <div className="text-gray-400 dark:text-gray-500 mb-4">
                  <UserGroupIcon className="mx-auto h-12 w-12" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  暂无推荐用户
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  当前没有推荐关注的用户。
                </p>
              </div>
            ) : (
              <InfiniteScroll
                dataLength={suggestedUsers.length}
                next={() => fetchSuggestedUsers(usersPage)}
                hasMore={hasMoreUsers}
                loader={
                  <div className="flex justify-center py-4">
                    <LoadingSpinner size="md" />
                  </div>
                }
                endMessage={
                  suggestedUsers.length > 0 && (
                    <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                      没有更多用户了
                    </div>
                  )
                }
              >
                <div className="divide-y divide-gray-200 dark:divide-dark-300">
                  {suggestedUsers.map((suggestedUser) => (
                    <div key={suggestedUser._id} className="p-4 flex items-center justify-between">
                      <UserCard user={suggestedUser} showBio />
                      <button
                        onClick={() => handleFollowUser(suggestedUser._id)}
                        disabled={followingUsers.has(suggestedUser._id)}
                        className={`px-4 py-1.5 text-sm font-medium rounded-lg transition-colors ${
                          suggestedUser.isFollowing
                            ? 'bg-gray-200 dark:bg-dark-300 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-dark-400'
                            : 'bg-blue-600 text-white hover:bg-blue-700'
                        } disabled:opacity-50`}
                      >
                        {followingUsers.has(suggestedUser._id) ? (
                          <LoadingSpinner size="sm" color={suggestedUser.isFollowing ? 'gray' : 'white'} />
                        ) : (
                          suggestedUser.isFollowing ? '已关注' : '关注'
                        )}
                      </button>
                    </div>
                  ))}
                </div>
              </InfiniteScroll>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default Trends;