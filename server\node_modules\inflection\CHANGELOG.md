# History

## 1.13.2 / 2022-01-29

- [fix] `drives` singular form
- [feat] allow `inflect` to use floats
- [chore] upgrade packages

## 1.13.1 / 2021-05-21

- [fix] use correct version for `inflector.version`
- [build] reduce npm bundle size by excluding more files
- [build] use terser to create a minified file

## 1.13.0 / 2021-05-01

- [update packages] mocha->8.3.2, should->13.2.3
- [bug fix] `grammar` plural form
- [bug fix] `bonus` plural form
- [bug fix] `octopus` plural form
- [bug fix] `virus` plural form
- [info] add LICENSE file
- [info] additional maintainer @p-kuen. @ben-l<PERSON> thanks for your trust!

## 1.12.0 / 2017-01-27

- [update packages] mocha->3.2.0, should->11.2.0
- [bug fix] `minus` plural & singular form
- [bug fix] `save` plural & singular form

## 1.11.0 / 2016-04-20

- [update packages] mocha->3.0.2, should->11.1.0
- [bug fix] `inflection.transform` in ES6

## 1.10.0 / 2016-04-20

- [update packages] should->8.3.1
- [bug fix] `campus` plural & singular form

## 1.9.0 / 2016-04-06

- [update packages] mocha->2.4.5, should->8.3.0
- [bug fix] `genus` plural & singular form

## 1.8.0 / 2015-11-22

- [update packages] mocha->2.3.4, should->7.1.1
- [bug fix] `criterion` plural & singular form

## 1.7.2 / 2015-10-11

- [update packages] mocha->2.3.3, should->7.1.0

## 1.7.1 / 2015-03-25

- [bug fix] `woman` plural & singular form

## 1.7.0 / 2015-03-25

- [bug fix] `canvas` plural & singular form
- [update packages] mocha->2.2.1, should->5.2.0

## 1.6.0 / 2014-12-06

- [bug fix] Special rules for index, vertex, and matrix masked by general rule x
- [update packages] mocha->2.1.0, should->4.6.5

## 1.5.3 / 2014-12-06

- [bug fix] Remove invalid uncountable words

## 1.5.2 / 2014-11-14

- [bug fix] `business` & `access` plural form

## 1.5.1 / 2014-09-23

- [bug fix] Fix `whereas` plural & singular form

## 1.5.0 / 2014-09-23

- [refactor] Add more rules and uncountable nouns

## 1.4.2 / 2014-09-05

- [bug fix] Fix wrong implementation of `goose`, `tooth` & `foot`

## 1.4.1 / 2014-08-31

- [bug fix] Fix `goose`, `tooth` & `foot` plural & singular form

## 1.4.0 / 2014-08-23

- [new feature] Adds support for an `inflect` method that will choose to pluralize or singularize a noun based on an integer value

## 1.3.8 / 2014-07-03

- [others] Syntax tuning

## 1.3.7 / 2014-06-25

- [refactor] Adopt UMD import to work in a variety of different situations
- [update packages] should->4.0.4

## 1.3.6 / 2014-06-07

- [bug fix] Rearrange rules. `movies`->`movy`

## 1.3.5 / 2014-02-12

- Unable to publsih v1.3.4 therefore jump to v1.3.5

## 1.3.4 / 2014-02-12

- [update packages] should->3.1.2
- [refactor] Use `mocha` instead of hard coding tests

## 1.3.3 / 2014-01-22

- [update packages] should->3.0.1
- Added brower.json

## 1.3.2 / 2013-12-12

- [update packages] node.flow->1.2.3

## 1.3.1 / 2013-12-12

- [refactor] Support `Requirejs`

## 1.3.0 / 2013-12-11

- [refactor] Move `var` out of loops
- [refactor] Change the way `camelize` acts to mimic 100% `Rails ActiveSupport Inflector camelize`

## 1.2.7 / 2013-12-11

- [new feature] Added transform, thnaks to `luk3thomas`
- [update packages] should->v2.1.1

## 1.2.6 / 2013-05-24

- [bug fix] Use instance instead of `this`

## 1.2.5 / 2013-01-09

- [refactor] Allow all caps strings to be returned from underscore

## 1.2.4 / 2013-01-06

- [bug fix] window obj does not have `call` method

## 1.2.3 / 2012-08-02

- [bug fix] Singularize `status` produces `statu`
- [update packages] should->v1.1.0

## 1.2.2 / 2012-07-23

- [update packages] node.flow->v1.1.3 & should->v1.0.0

## 1.2.1 / 2012-06-22

- [bug fix] Singularize `address` produces `addres`

## 1.2.0 / 2012-04-10

- [new feature] Browser support
- [update packages] node.flow->v1.1.1

## 1.1.1 / 2012-02-13

- [update packages] node.flow->v1.1.0

## 1.1.0 / 2012-02-13

- [update packages] node.flow->v1.0.0
- [refactor] Read version number from package.json

## 1.0.0 / 2012-02-08

- Remove make file
- Add pluralize rules
- Add pluralize tests
- [refactor] Use object.jey instead of for in

## 0.0.1 / 2012-01-16

- Initial release
