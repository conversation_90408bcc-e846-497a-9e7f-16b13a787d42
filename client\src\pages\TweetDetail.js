import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useSocket } from '../contexts/SocketContext';
import axios from 'axios';
import toast from 'react-hot-toast';
import moment from 'moment';
import {
  ArrowLeftIcon,
  ChatBubbleOvalLeftIcon,
  ArrowPathIcon,
  HeartIcon,
  ShareIcon,
  EllipsisHorizontalIcon
} from '@heroicons/react/24/outline';
import {
  HeartIcon as HeartIconSolid,
  ArrowPathIcon as ArrowPathIconSolid
} from '@heroicons/react/24/solid';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import TweetComposer from '../components/Tweet/TweetComposer';
import TweetCard from '../components/Tweet/TweetCard';

function TweetDetail() {
  const { tweetId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { emit } = useSocket();
  const [tweet, setTweet] = useState(null);
  const [replies, setReplies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [repliesLoading, setRepliesLoading] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [isRetweeted, setIsRetweeted] = useState(false);
  const [likesCount, setLikesCount] = useState(0);
  const [retweetsCount, setRetweetsCount] = useState(0);
  const [repliesCount, setRepliesCount] = useState(0);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    fetchTweet();
    fetchReplies();
  }, [tweetId]);

  // Listen for real-time updates
  useEffect(() => {
    const handleNewReply = (event) => {
      const { reply } = event.detail;
      if (reply.replyTo === tweetId) {
        setReplies(prev => [reply, ...prev]);
        setRepliesCount(prev => prev + 1);
      }
    };

    const handleTweetLiked = (event) => {
      const { tweetId: likedTweetId, isLiked: liked, likesCount: count } = event.detail;
      if (likedTweetId === tweetId) {
        setIsLiked(liked);
        setLikesCount(count);
      }
    };

    const handleTweetRetweeted = (event) => {
      const { tweetId: retweetedTweetId, isRetweeted: retweeted, retweetsCount: count } = event.detail;
      if (retweetedTweetId === tweetId) {
        setIsRetweeted(retweeted);
        setRetweetsCount(count);
      }
    };

    window.addEventListener('newReply', handleNewReply);
    window.addEventListener('tweetLiked', handleTweetLiked);
    window.addEventListener('tweetRetweeted', handleTweetRetweeted);

    return () => {
      window.removeEventListener('newReply', handleNewReply);
      window.removeEventListener('tweetLiked', handleTweetLiked);
      window.removeEventListener('tweetRetweeted', handleTweetRetweeted);
    };
  }, [tweetId]);

  const fetchTweet = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/tweets/${tweetId}`);
      const tweetData = response.data;
      setTweet(tweetData);
      setIsLiked(tweetData.isLiked || false);
      setIsRetweeted(tweetData.isRetweeted || false);
      setLikesCount(tweetData.likesCount || 0);
      setRetweetsCount(tweetData.retweetsCount || 0);
      setRepliesCount(tweetData.repliesCount || 0);
    } catch (error) {
      console.error('Error fetching tweet:', error);
      if (error.response?.status === 404) {
        toast.error('推文不存在');
        navigate('/');
      } else {
        toast.error('加载推文失败');
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchReplies = async (pageNum = 1) => {
    try {
      setRepliesLoading(true);
      const response = await axios.get(`/tweets/${tweetId}/replies`, {
        params: { page: pageNum, limit: 20 }
      });
      const { replies: newReplies, hasMore: moreReplies } = response.data;
      
      if (pageNum === 1) {
        setReplies(newReplies);
      } else {
        setReplies(prev => [...prev, ...newReplies]);
      }
      setHasMore(moreReplies);
      setPage(pageNum);
    } catch (error) {
      console.error('Error fetching replies:', error);
      toast.error('加载回复失败');
    } finally {
      setRepliesLoading(false);
    }
  };

  const handleLike = async () => {
    try {
      const response = await axios.post(`/tweets/${tweetId}/like`);
      const newIsLiked = response.data.isLiked;
      const newCount = response.data.likesCount;
      
      setIsLiked(newIsLiked);
      setLikesCount(newCount);
      
      // Emit real-time event
      emit('tweet_liked', {
        tweetId,
        isLiked: newIsLiked,
        likesCount: newCount,
        userId: user._id
      });
    } catch (error) {
      toast.error('操作失败，请重试');
    }
  };

  const handleRetweet = async () => {
    try {
      const response = await axios.post(`/tweets/${tweetId}/retweet`);
      const newIsRetweeted = response.data.isRetweeted;
      const newCount = response.data.retweetsCount;
      
      setIsRetweeted(newIsRetweeted);
      setRetweetsCount(newCount);
      
      // Emit real-time event
      emit('tweet_retweeted', {
        tweetId,
        isRetweeted: newIsRetweeted,
        retweetsCount: newCount,
        userId: user._id
      });
      
      toast.success(newIsRetweeted ? '转发成功' : '取消转发');
    } catch (error) {
      toast.error('操作失败，请重试');
    }
  };

  const handleShare = async () => {
    const url = `${window.location.origin}/tweet/${tweetId}`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${tweet.author.displayName || tweet.author.username} 的推文`,
          text: tweet.content,
          url: url
        });
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      // Fallback to clipboard
      try {
        await navigator.clipboard.writeText(url);
        toast.success('链接已复制到剪贴板');
      } catch (error) {
        toast.error('分享失败');
      }
    }
  };

  const handleReplySuccess = (newReply) => {
    setReplies(prev => [newReply, ...prev]);
    setRepliesCount(prev => prev + 1);
    
    // Emit real-time event
    emit('new_reply', {
      reply: newReply,
      tweetId
    });
  };

  const handleLoadMoreReplies = () => {
    if (!repliesLoading && hasMore) {
      fetchReplies(page + 1);
    }
  };

  const formatContent = (content) => {
    return content
      .replace(/#(\w+)/g, '<span class="hashtag">#$1</span>')
      .replace(/@(\w+)/g, '<span class="mention">@$1</span>')
      .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" class="link" target="_blank" rel="noopener noreferrer">$1</a>');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white dark:bg-dark-100">
        <div className="max-w-2xl mx-auto">
          <div className="sticky top-0 z-10 bg-white/80 dark:bg-dark-100/80 backdrop-blur-md border-b border-gray-200 dark:border-dark-300">
            <div className="flex items-center space-x-4 p-4">
              <button
                onClick={() => navigate(-1)}
                className="p-2 hover:bg-gray-100 dark:hover:bg-dark-300 rounded-full transition-colors"
              >
                <ArrowLeftIcon className="w-5 h-5 text-gray-900 dark:text-white" />
              </button>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">推文</h1>
            </div>
          </div>
          <div className="flex justify-center items-center py-20">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </div>
    );
  }

  if (!tweet) {
    return (
      <div className="min-h-screen bg-white dark:bg-dark-100">
        <div className="max-w-2xl mx-auto">
          <div className="sticky top-0 z-10 bg-white/80 dark:bg-dark-100/80 backdrop-blur-md border-b border-gray-200 dark:border-dark-300">
            <div className="flex items-center space-x-4 p-4">
              <button
                onClick={() => navigate(-1)}
                className="p-2 hover:bg-gray-100 dark:hover:bg-dark-300 rounded-full transition-colors"
              >
                <ArrowLeftIcon className="w-5 h-5 text-gray-900 dark:text-white" />
              </button>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">推文</h1>
            </div>
          </div>
          <div className="text-center py-20">
            <p className="text-gray-500 dark:text-gray-400">推文不存在</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white dark:bg-dark-100">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="sticky top-0 z-10 bg-white/80 dark:bg-dark-100/80 backdrop-blur-md border-b border-gray-200 dark:border-dark-300">
          <div className="flex items-center space-x-4 p-4">
            <button
              onClick={() => navigate(-1)}
              className="p-2 hover:bg-gray-100 dark:hover:bg-dark-300 rounded-full transition-colors"
            >
              <ArrowLeftIcon className="w-5 h-5 text-gray-900 dark:text-white" />
            </button>
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">推文</h1>
          </div>
        </div>

        {/* Tweet Detail */}
        <article className="border-b border-gray-200 dark:border-dark-300 p-6">
          {/* Author info */}
          <div className="flex items-center space-x-3 mb-4">
            <img
              src={tweet.author.avatar || '/default-avatar.png'}
              alt={tweet.author.displayName || tweet.author.username}
              className="w-12 h-12 rounded-full"
            />
            <div>
              <div className="flex items-center space-x-2">
                <h2 className="font-bold text-gray-900 dark:text-white">
                  {tweet.author.displayName || tweet.author.username}
                </h2>
                {tweet.author.verified && (
                  <svg className="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
              <p className="text-gray-500 dark:text-gray-400">@{tweet.author.username}</p>
            </div>
          </div>

          {/* Tweet content */}
          <div className="mb-4">
            <p 
              className="text-xl text-gray-900 dark:text-white leading-relaxed"
              dangerouslySetInnerHTML={{ __html: formatContent(tweet.content) }}
            />
          </div>

          {/* Images */}
          {tweet.images && tweet.images.length > 0 && (
            <div className={`mb-4 grid gap-2 rounded-lg overflow-hidden ${
              tweet.images.length === 1 ? 'grid-cols-1' :
              tweet.images.length === 2 ? 'grid-cols-2' :
              tweet.images.length === 3 ? 'grid-cols-2' :
              'grid-cols-2'
            }`}>
              {tweet.images.map((image, index) => (
                <div 
                  key={index} 
                  className={tweet.images.length === 3 && index === 0 ? 'row-span-2' : ''}
                >
                  <img
                    src={image}
                    alt="Tweet image"
                    className="w-full h-full object-cover hover:opacity-95 transition-opacity cursor-pointer"
                    style={{ maxHeight: tweet.images.length === 1 ? '500px' : '250px' }}
                  />
                </div>
              ))}
            </div>
          )}

          {/* Quote tweet */}
          {tweet.quoteTweet && (
            <div className="border border-gray-200 dark:border-dark-300 rounded-lg p-4 mb-4">
              <div className="flex items-center space-x-2 mb-2">
                <img
                  src={tweet.quoteTweet.author.avatar || '/default-avatar.png'}
                  alt={tweet.quoteTweet.author.displayName}
                  className="w-6 h-6 rounded-full"
                />
                <span className="font-medium text-gray-900 dark:text-white">
                  {tweet.quoteTweet.author.displayName || tweet.quoteTweet.author.username}
                </span>
                <span className="text-gray-500 dark:text-gray-400">
                  @{tweet.quoteTweet.author.username}
                </span>
                <span className="text-gray-500 dark:text-gray-400">·</span>
                <span className="text-gray-500 dark:text-gray-400">
                  {moment(tweet.quoteTweet.createdAt).fromNow()}
                </span>
              </div>
              <p className="text-gray-900 dark:text-white">
                {tweet.quoteTweet.content}
              </p>
            </div>
          )}

          {/* Timestamp */}
          <div className="text-gray-500 dark:text-gray-400 text-sm mb-4 pb-4 border-b border-gray-200 dark:border-dark-300">
            {moment(tweet.createdAt).format('h:mm A · MMM D, YYYY')}
          </div>

          {/* Stats */}
          <div className="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400 mb-4 pb-4 border-b border-gray-200 dark:border-dark-300">
            {retweetsCount > 0 && (
              <span>
                <span className="font-bold text-gray-900 dark:text-white">{retweetsCount}</span> 转发
              </span>
            )}
            {likesCount > 0 && (
              <span>
                <span className="font-bold text-gray-900 dark:text-white">{likesCount}</span> 喜欢
              </span>
            )}
            {repliesCount > 0 && (
              <span>
                <span className="font-bold text-gray-900 dark:text-white">{repliesCount}</span> 回复
              </span>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center justify-around py-2 border-b border-gray-200 dark:border-dark-300">
            <button className="tweet-action group">
              <div className="p-3 rounded-full group-hover:bg-blue-50 dark:group-hover:bg-blue-900/20 transition-colors">
                <ChatBubbleOvalLeftIcon className="w-6 h-6" />
              </div>
            </button>

            <button
              onClick={handleRetweet}
              className={`tweet-action group ${isRetweeted ? 'retweeted' : ''}`}
            >
              <div className="p-3 rounded-full group-hover:bg-green-50 dark:group-hover:bg-green-900/20 transition-colors">
                {isRetweeted ? (
                  <ArrowPathIconSolid className="w-6 h-6" />
                ) : (
                  <ArrowPathIcon className="w-6 h-6" />
                )}
              </div>
            </button>

            <button
              onClick={handleLike}
              className={`tweet-action group ${isLiked ? 'liked' : ''}`}
            >
              <div className="p-3 rounded-full group-hover:bg-red-50 dark:group-hover:bg-red-900/20 transition-colors">
                {isLiked ? (
                  <HeartIconSolid className="w-6 h-6" />
                ) : (
                  <HeartIcon className="w-6 h-6" />
                )}
              </div>
            </button>

            <button
              onClick={handleShare}
              className="tweet-action group"
            >
              <div className="p-3 rounded-full group-hover:bg-blue-50 dark:group-hover:bg-blue-900/20 transition-colors">
                <ShareIcon className="w-6 h-6" />
              </div>
            </button>
          </div>
        </article>

        {/* Reply Composer */}
        <div className="border-b border-gray-200 dark:border-dark-300 p-4">
          <TweetComposer
            replyTo={tweet}
            onSuccess={handleReplySuccess}
            placeholder={`回复 @${tweet.author.username}`}
          />
        </div>

        {/* Replies */}
        <div>
          {replies.length > 0 ? (
            <>
              {replies.map((reply) => (
                <TweetCard
                  key={reply._id}
                  tweet={reply}
                  onUpdate={(tweetId, updates) => {
                    setReplies(prev => prev.map(r => 
                      r._id === tweetId ? { ...r, ...updates } : r
                    ));
                  }}
                  onDelete={(tweetId) => {
                    setReplies(prev => prev.filter(r => r._id !== tweetId));
                    setRepliesCount(prev => prev - 1);
                  }}
                />
              ))}
              
              {hasMore && (
                <div className="p-4 text-center">
                  <button
                    onClick={handleLoadMoreReplies}
                    disabled={repliesLoading}
                    className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                  >
                    {repliesLoading ? '加载中...' : '查看更多回复'}
                  </button>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <ChatBubbleOvalLeftIcon className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">还没有回复</p>
              <p className="text-gray-400 dark:text-gray-500 text-sm mt-1">成为第一个回复的人</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default TweetDetail;