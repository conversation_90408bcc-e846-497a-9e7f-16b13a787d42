const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const TweetLike = sequelize.define('TweetLike', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  tweetId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'tweets',
      key: 'id'
    }
  },
  userId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'tweet_likes',
  indexes: [
    {
      unique: true,
      fields: ['tweet_id', 'user_id']
    },
    {
      fields: ['tweet_id']
    },
    {
      fields: ['user_id']
    }
  ]
});

module.exports = TweetLike;