const sequelize = require('../config/database');
const User = require('./User');
const Tweet = require('./Tweet');
const Notification = require('./Notification');
const Follow = require('./Follow');
const TweetLike = require('./TweetLike');
const TweetRetweet = require('./TweetRetweet');

// Define associations

// User associations
User.hasMany(Tweet, { foreignKey: 'authorId', as: 'tweets' });
User.hasMany(Notification, { foreignKey: 'recipientId', as: 'receivedNotifications' });
User.hasMany(Notification, { foreignKey: 'senderId', as: 'sentNotifications' });
User.hasMany(TweetLike, { foreignKey: 'userId', as: 'likes' });
User.hasMany(TweetRetweet, { foreignKey: 'userId', as: 'retweets' });

// Follow associations
User.hasMany(Follow, { foreignKey: 'followerId', as: 'following' });
User.hasMany(Follow, { foreignKey: 'followingId', as: 'followers' });
Follow.belongsTo(User, { foreignKey: 'followerId', as: 'follower' });
Follow.belongsTo(User, { foreignKey: 'followingId', as: 'following' });

// Tweet associations
Tweet.belongsTo(User, { foreignKey: 'authorId', as: 'author' });
Tweet.belongsTo(Tweet, { foreignKey: 'replyToId', as: 'replyTo' });
Tweet.belongsTo(Tweet, { foreignKey: 'quoteTweetId', as: 'quoteTweet' });
Tweet.belongsTo(Tweet, { foreignKey: 'originalTweetId', as: 'originalTweet' });
Tweet.hasMany(Tweet, { foreignKey: 'replyToId', as: 'replies' });
Tweet.hasMany(TweetLike, { foreignKey: 'tweetId', as: 'likes' });
Tweet.hasMany(TweetRetweet, { foreignKey: 'tweetId', as: 'retweets' });
Tweet.hasMany(Notification, { foreignKey: 'tweetId', as: 'notifications' });

// TweetLike associations
TweetLike.belongsTo(User, { foreignKey: 'userId', as: 'user' });
TweetLike.belongsTo(Tweet, { foreignKey: 'tweetId', as: 'tweet' });

// TweetRetweet associations
TweetRetweet.belongsTo(User, { foreignKey: 'userId', as: 'user' });
TweetRetweet.belongsTo(Tweet, { foreignKey: 'tweetId', as: 'tweet' });

// Notification associations
Notification.belongsTo(User, { foreignKey: 'recipientId', as: 'recipient' });
Notification.belongsTo(User, { foreignKey: 'senderId', as: 'sender' });
Notification.belongsTo(Tweet, { foreignKey: 'tweetId', as: 'tweet' });

module.exports = {
  sequelize,
  User,
  Tweet,
  Notification,
  Follow,
  TweetLike,
  TweetRetweet
};