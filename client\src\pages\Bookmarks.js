import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useSocket } from '../contexts/SocketContext';
import axios from 'axios';
import toast from 'react-hot-toast';
import InfiniteScroll from 'react-infinite-scroll-component';
import {
  BookmarkIcon as BookmarkIconOutline,
  TrashIcon
} from '@heroicons/react/24/outline';
import {
  BookmarkIcon as BookmarkIconSolid
} from '@heroicons/react/24/solid';
import TweetCard from '../components/Tweet/TweetCard';
import LoadingSpinner from '../components/UI/LoadingSpinner';

function Bookmarks() {
  const { user } = useAuth();
  const { socket } = useSocket();
  const [tweets, setTweets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [showClearModal, setShowClearModal] = useState(false);
  const [clearing, setClearing] = useState(false);

  useEffect(() => {
    if (user) {
      fetchBookmarks();
    }
  }, [user]);

  useEffect(() => {
    if (socket) {
      socket.on('tweetDeleted', handleTweetDeleted);
      socket.on('tweetUpdated', handleTweetUpdated);

      return () => {
        socket.off('tweetDeleted', handleTweetDeleted);
        socket.off('tweetUpdated', handleTweetUpdated);
      };
    }
  }, [socket]);

  const fetchBookmarks = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/users/bookmarks?page=1&limit=20');
      setTweets(response.data.tweets);
      setHasMore(response.data.hasMore);
      setPage(2);
    } catch (error) {
      console.error('Error fetching bookmarks:', error);
      toast.error('获取书签失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchMoreBookmarks = async () => {
    try {
      const response = await axios.get(`/api/users/bookmarks?page=${page}&limit=20`);
      setTweets(prev => [...prev, ...response.data.tweets]);
      setHasMore(response.data.hasMore);
      setPage(prev => prev + 1);
    } catch (error) {
      console.error('Error fetching more bookmarks:', error);
      toast.error('加载更多失败');
    }
  };

  const handleTweetUpdate = (updatedTweet) => {
    setTweets(prev => prev.map(tweet => 
      tweet._id === updatedTweet._id ? updatedTweet : tweet
    ));
  };

  const handleTweetDelete = (tweetId) => {
    setTweets(prev => prev.filter(tweet => tweet._id !== tweetId));
  };

  const handleTweetDeleted = (data) => {
    handleTweetDelete(data.tweetId);
  };

  const handleTweetUpdated = (data) => {
    handleTweetUpdate(data.tweet);
  };

  const handleBookmarkToggle = (tweetId, isBookmarked) => {
    if (!isBookmarked) {
      // If unbookmarked, remove from bookmarks list
      handleTweetDelete(tweetId);
    }
  };

  const handleClearAllBookmarks = async () => {
    try {
      setClearing(true);
      await axios.delete('/api/users/bookmarks');
      setTweets([]);
      setHasMore(false);
      setShowClearModal(false);
      toast.success('已清空所有书签');
    } catch (error) {
      console.error('Error clearing bookmarks:', error);
      toast.error('清空书签失败');
    } finally {
      setClearing(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white dark:bg-dark-100">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white/80 dark:bg-dark-100/80 backdrop-blur-md border-b border-gray-200 dark:border-dark-300">
        <div className="max-w-2xl mx-auto px-4 py-3 flex items-center justify-between">
          <div>
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">书签</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              @{user?.username} • {tweets.length} 条推文
            </p>
          </div>
          {tweets.length > 0 && (
            <button
              onClick={() => setShowClearModal(true)}
              className="flex items-center space-x-1 px-3 py-1.5 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
            >
              <TrashIcon className="h-4 w-4" />
              <span>清空</span>
            </button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="max-w-2xl mx-auto">
        {tweets.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 dark:text-gray-500 mb-4">
              <BookmarkIconOutline className="mx-auto h-12 w-12" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              保存您喜欢的推文
            </h3>
            <p className="text-gray-500 dark:text-gray-400 max-w-sm mx-auto">
              点击推文上的书签图标来保存推文，这样您就可以轻松地稍后阅读。
            </p>
          </div>
        ) : (
          <InfiniteScroll
            dataLength={tweets.length}
            next={fetchMoreBookmarks}
            hasMore={hasMore}
            loader={
              <div className="flex justify-center py-4">
                <LoadingSpinner size="sm" />
              </div>
            }
            endMessage={
              <div className="text-center py-4 text-gray-500 dark:text-gray-400 text-sm">
                没有更多书签了
              </div>
            }
          >
            <div className="divide-y divide-gray-200 dark:divide-dark-300">
              {tweets.map((tweet) => (
                <TweetCard
                  key={tweet._id}
                  tweet={tweet}
                  onUpdate={handleTweetUpdate}
                  onDelete={handleTweetDelete}
                  onBookmarkToggle={handleBookmarkToggle}
                />
              ))}
            </div>
          </InfiniteScroll>
        )}
      </div>

      {/* Clear All Bookmarks Modal */}
      {showClearModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-dark-200 rounded-lg w-full max-w-md">
            <div className="p-6">
              <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 dark:bg-red-900/20 rounded-full mb-4">
                <TrashIcon className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white text-center mb-2">
                清空所有书签
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 text-center mb-6">
                此操作将删除您保存的所有书签。此操作无法撤销。
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowClearModal(false)}
                  disabled={clearing}
                  className="flex-1 px-4 py-2 border border-gray-300 dark:border-dark-300 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-200 hover:bg-gray-50 dark:hover:bg-dark-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={handleClearAllBookmarks}
                  disabled={clearing}
                  className="flex-1 px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {clearing ? <LoadingSpinner size="sm" color="white" /> : '清空书签'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Bookmarks;