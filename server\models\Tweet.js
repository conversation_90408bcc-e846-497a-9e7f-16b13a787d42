const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Tweet = sequelize.define('Tweet', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  authorId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  content: {
    type: DataTypes.STRING(280),
    allowNull: false
  },
  images: {
    type: DataTypes.JSONB,
    defaultValue: []
  },
  hashtags: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    defaultValue: []
  },
  mentions: {
    type: DataTypes.ARRAY(DataTypes.UUID),
    defaultValue: []
  },
  replyToId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'tweets',
      key: 'id'
    }
  },
  quoteTweetId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'tweets',
      key: 'id'
    }
  },
  likesCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  retweetsCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  repliesCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  viewsCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  isReply: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  isRetweet: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  originalTweetId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'tweets',
      key: 'id'
    }
  },
  visibility: {
    type: DataTypes.ENUM('public', 'followers', 'mentioned'),
    defaultValue: 'public'
  },
  isPinned: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  isDeleted: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  editHistory: {
    type: DataTypes.JSONB,
    defaultValue: []
  },
  lastEditedAt: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'tweets',
  indexes: [
    {
      fields: ['author_id', 'created_at']
    },
    {
      fields: ['hashtags'],
      using: 'gin'
    },
    {
      fields: ['mentions'],
      using: 'gin'
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['reply_to_id']
    },
    {
      fields: ['is_deleted']
    },
    {
       fields: ['content']
     }
  ],
  hooks: {
    beforeSave: (tweet) => {
      if (tweet.changed('content')) {
        // Extract hashtags
        const hashtagRegex = /#([a-zA-Z0-9_]+)/g;
        const hashtags = [];
        let match;
        while ((match = hashtagRegex.exec(tweet.content)) !== null) {
          hashtags.push(match[1].toLowerCase());
        }
        tweet.hashtags = [...new Set(hashtags)]; // Remove duplicates
      }
    }
  }
});

// Instance methods
Tweet.prototype.isLikedBy = async function(userId) {
  const TweetLike = require('./TweetLike');
  const like = await TweetLike.findOne({
    where: { tweetId: this.id, userId: userId }
  });
  return !!like;
};

Tweet.prototype.isRetweetedBy = async function(userId) {
  const TweetRetweet = require('./TweetRetweet');
  const retweet = await TweetRetweet.findOne({
    where: { tweetId: this.id, userId: userId }
  });
  return !!retweet;
};

Tweet.prototype.toggleLike = async function(userId) {
  const TweetLike = require('./TweetLike');
  const existingLike = await TweetLike.findOne({
    where: { tweetId: this.id, userId: userId }
  });
  
  if (existingLike) {
    await existingLike.destroy();
    await this.decrement('likesCount');
    return false; // unliked
  } else {
    await TweetLike.create({ tweetId: this.id, userId: userId });
    await this.increment('likesCount');
    return true; // liked
  }
};

Tweet.prototype.toggleRetweet = async function(userId) {
  const TweetRetweet = require('./TweetRetweet');
  const existingRetweet = await TweetRetweet.findOne({
    where: { tweetId: this.id, userId: userId }
  });
  
  if (existingRetweet) {
    await existingRetweet.destroy();
    await this.decrement('retweetsCount');
    return false; // unretweeted
  } else {
    await TweetRetweet.create({ tweetId: this.id, userId: userId });
    await this.increment('retweetsCount');
    return true; // retweeted
  }
};

Tweet.prototype.incrementViews = async function() {
  await this.increment('viewsCount');
};

// Static methods
Tweet.getTrendingHashtags = async function(limit = 10) {
  const { QueryTypes } = require('sequelize');
  
  const trends = await sequelize.query(`
    SELECT 
      unnest(hashtags) as hashtag,
      COUNT(*) as count,
      COUNT(DISTINCT id) as tweet_count
    FROM tweets 
    WHERE 
      created_at >= NOW() - INTERVAL '24 hours' 
      AND is_deleted = false
      AND array_length(hashtags, 1) > 0
    GROUP BY hashtag
    ORDER BY count DESC
    LIMIT :limit
  `, {
    replacements: { limit },
    type: QueryTypes.SELECT
  });
  
  return trends;
};

module.exports = Tweet;