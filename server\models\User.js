const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const sequelize = require('../config/database');

const User = sequelize.define('User', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  username: {
    type: DataTypes.STRING(20),
    allowNull: false,
    unique: true,
    validate: {
      len: [3, 20],
      is: /^[a-zA-Z0-9_]+$/
    }
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      len: [6, 255]
    }
  },
  displayName: {
    type: DataTypes.STRING(50),
    allowNull: false,
    validate: {
      len: [1, 50]
    }
  },
  bio: {
    type: DataTypes.STRING(160),
    defaultValue: ''
  },
  location: {
    type: DataTypes.STRING(30),
    defaultValue: ''
  },
  website: {
    type: DataTypes.STRING(100),
    defaultValue: ''
  },
  avatar: {
    type: DataTypes.TEXT,
    defaultValue: ''
  },
  coverImage: {
    type: DataTypes.TEXT,
    defaultValue: ''
  },
  tweetsCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  followersCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  followingCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  likesCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  isVerified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  isPrivate: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  joinedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  lastActive: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  preferences: {
    type: DataTypes.JSONB,
    defaultValue: {
      emailNotifications: true,
      pushNotifications: true,
      darkMode: false
    }
  }
}, {
  tableName: 'users',
  indexes: [
    {
      unique: true,
      fields: ['email']
    },
    {
      unique: true,
      fields: ['username']
    },
    {
       fields: ['display_name']
     },
     {
       fields: ['bio']
     }
  ],
  hooks: {
    beforeSave: async (user) => {
      if (user.changed('password')) {
        const salt = await bcrypt.genSalt(12);
        user.password = await bcrypt.hash(user.password, salt);
      }
    }
  }
});

// Instance methods
User.prototype.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

User.prototype.updateLastActive = async function() {
  this.lastActive = new Date();
  return this.save();
};

User.prototype.getPublicProfile = function() {
  const userObject = this.toJSON();
  delete userObject.password;
  delete userObject.email;
  return userObject;
};

User.prototype.follow = async function(userId) {
  const Follow = require('./Follow');
  const existingFollow = await Follow.findOne({
    where: { followerId: this.id, followingId: userId }
  });
  
  if (!existingFollow) {
    await Follow.create({ followerId: this.id, followingId: userId });
    await this.increment('followingCount');
    await User.increment('followersCount', { where: { id: userId } });
  }
};

User.prototype.unfollow = async function(userId) {
  const Follow = require('./Follow');
  const deleted = await Follow.destroy({
    where: { followerId: this.id, followingId: userId }
  });
  
  if (deleted) {
    await this.decrement('followingCount');
    await User.decrement('followersCount', { where: { id: userId } });
  }
};

module.exports = User;