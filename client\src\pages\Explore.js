import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import toast from 'react-hot-toast';
import InfiniteScroll from 'react-infinite-scroll-component';
import {
  MagnifyingGlassIcon,
  FireIcon,
  SparklesIcon,
  HashtagIcon,
  TrendingUpIcon
} from '@heroicons/react/24/outline';
import TweetCard from '../components/Tweet/TweetCard';
import LoadingSpinner from '../components/UI/LoadingSpinner';

function Explore() {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('trending'); // trending, latest, top
  const [tweets, setTweets] = useState([]);
  const [trendingTopics, setTrendingTopics] = useState([]);
  const [suggestedUsers, setSuggestedUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchSuggestions, setSearchSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  useEffect(() => {
    fetchInitialData();
  }, [activeTab]);

  useEffect(() => {
    if (searchQuery.trim()) {
      const debounceTimer = setTimeout(() => {
        fetchSearchSuggestions();
      }, 300);
      return () => clearTimeout(debounceTimer);
    } else {
      setSearchSuggestions([]);
      setShowSuggestions(false);
    }
  }, [searchQuery]);

  const fetchInitialData = async () => {
    try {
      setLoading(true);
      setTweets([]);
      setPage(1);
      setHasMore(true);
      
      // Fetch tweets based on active tab
      let endpoint = '/tweets/explore';
      if (activeTab === 'latest') {
        endpoint = '/tweets/explore?sort=latest';
      } else if (activeTab === 'top') {
        endpoint = '/tweets/explore?sort=top';
      }
      
      const [tweetsResponse, trendingResponse, usersResponse] = await Promise.all([
        axios.get(endpoint, { params: { page: 1, limit: 20 } }),
        axios.get('/search/trending'),
        axios.get('/users/suggested', { params: { limit: 5 } })
      ]);
      
      setTweets(tweetsResponse.data.tweets);
      setHasMore(tweetsResponse.data.hasMore);
      setTrendingTopics(trendingResponse.data.hashtags || []);
      setSuggestedUsers(usersResponse.data.users || []);
    } catch (error) {
      console.error('Error fetching explore data:', error);
      toast.error('加载失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const fetchMoreTweets = async () => {
    if (!hasMore) return;
    
    try {
      let endpoint = '/tweets/explore';
      if (activeTab === 'latest') {
        endpoint = '/tweets/explore?sort=latest';
      } else if (activeTab === 'top') {
        endpoint = '/tweets/explore?sort=top';
      }
      
      const response = await axios.get(endpoint, {
        params: { page: page + 1, limit: 20 }
      });
      
      const newTweets = response.data.tweets;
      setTweets(prev => [...prev, ...newTweets]);
      setHasMore(response.data.hasMore);
      setPage(prev => prev + 1);
    } catch (error) {
      console.error('Error fetching more tweets:', error);
      toast.error('加载更多失败');
    }
  };

  const fetchSearchSuggestions = async () => {
    try {
      const response = await axios.get('/search/suggestions', {
        params: { q: searchQuery, limit: 5 }
      });
      setSearchSuggestions(response.data.suggestions || []);
      setShowSuggestions(true);
    } catch (error) {
      console.error('Error fetching search suggestions:', error);
    }
  };

  const handleSearch = (query = searchQuery) => {
    if (query.trim()) {
      navigate(`/search?q=${encodeURIComponent(query.trim())}`);
    }
  };

  const handleSearchInputChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    handleSearch();
    setShowSuggestions(false);
  };

  const handleSuggestionClick = (suggestion) => {
    if (suggestion.type === 'user') {
      navigate(`/${suggestion.username}`);
    } else if (suggestion.type === 'hashtag') {
      navigate(`/search?q=${encodeURIComponent('#' + suggestion.text)}`);
    } else {
      handleSearch(suggestion.text);
    }
    setShowSuggestions(false);
    setSearchQuery('');
  };

  const handleTweetUpdate = (tweetId, updates) => {
    setTweets(prev => prev.map(tweet => 
      tweet._id === tweetId ? { ...tweet, ...updates } : tweet
    ));
  };

  const handleTweetDelete = (tweetId) => {
    setTweets(prev => prev.filter(tweet => tweet._id !== tweetId));
  };

  const handleFollowUser = async (userId) => {
    try {
      await axios.post(`/users/${userId}/follow`);
      setSuggestedUsers(prev => prev.map(user => 
        user._id === userId ? { ...user, isFollowing: !user.isFollowing } : user
      ));
      toast.success('关注成功');
    } catch (error) {
      toast.error('操作失败，请重试');
    }
  };

  const tabs = [
    { id: 'trending', label: '热门', icon: FireIcon },
    { id: 'latest', label: '最新', icon: SparklesIcon },
    { id: 'top', label: '精选', icon: TrendingUpIcon }
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-dark-100">
      <div className="max-w-6xl mx-auto flex">
        {/* Main Content */}
        <div className="flex-1 max-w-2xl">
          {/* Header */}
          <div className="sticky top-0 z-10 bg-white/80 dark:bg-dark-100/80 backdrop-blur-md border-b border-gray-200 dark:border-dark-300">
            <div className="p-4">
              <h1 className="text-xl font-bold text-gray-900 dark:text-white mb-4">探索</h1>
              
              {/* Search Bar */}
              <div className="relative">
                <form onSubmit={handleSearchSubmit}>
                  <div className="relative">
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={handleSearchInputChange}
                      onFocus={() => searchSuggestions.length > 0 && setShowSuggestions(true)}
                      onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                      placeholder="搜索推文、用户、话题..."
                      className="w-full pl-10 pr-4 py-3 bg-gray-100 dark:bg-dark-200 border-0 rounded-full focus:ring-2 focus:ring-blue-500 focus:bg-white dark:focus:bg-dark-100 transition-colors text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                    />
                  </div>
                </form>
                
                {/* Search Suggestions */}
                {showSuggestions && searchSuggestions.length > 0 && (
                  <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-dark-200 rounded-lg shadow-lg border border-gray-200 dark:border-dark-300 z-20">
                    {searchSuggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => handleSuggestionClick(suggestion)}
                        className="w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-dark-300 transition-colors flex items-center space-x-3"
                      >
                        {suggestion.type === 'user' ? (
                          <>
                            <img
                              src={suggestion.avatar || '/default-avatar.png'}
                              alt={suggestion.displayName}
                              className="w-8 h-8 rounded-full"
                            />
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">
                                {suggestion.displayName || suggestion.username}
                              </p>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                @{suggestion.username}
                              </p>
                            </div>
                          </>
                        ) : suggestion.type === 'hashtag' ? (
                          <>
                            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                              <HashtagIcon className="w-4 h-4 text-blue-500" />
                            </div>
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">
                                #{suggestion.text}
                              </p>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                {suggestion.count} 条推文
                              </p>
                            </div>
                          </>
                        ) : (
                          <>
                            <div className="w-8 h-8 bg-gray-100 dark:bg-dark-300 rounded-full flex items-center justify-center">
                              <MagnifyingGlassIcon className="w-4 h-4 text-gray-500" />
                            </div>
                            <p className="text-gray-900 dark:text-white">{suggestion.text}</p>
                          </>
                        )}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
            
            {/* Tabs */}
            <div className="flex border-b border-gray-200 dark:border-dark-300">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex-1 flex items-center justify-center space-x-2 py-4 px-4 font-medium transition-colors relative ${
                      activeTab === tab.id
                        ? 'text-blue-500 dark:text-blue-400'
                        : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{tab.label}</span>
                    {activeTab === tab.id && (
                      <div className="absolute bottom-0 left-0 right-0 h-1 bg-blue-500 dark:bg-blue-400 rounded-full" />
                    )}
                  </button>
                );
              })}
            </div>
          </div>

          {/* Content */}
          <div>
            {loading ? (
              <div className="flex justify-center items-center py-20">
                <LoadingSpinner size="lg" />
              </div>
            ) : tweets.length > 0 ? (
              <InfiniteScroll
                dataLength={tweets.length}
                next={fetchMoreTweets}
                hasMore={hasMore}
                loader={<div className="flex justify-center py-4"><LoadingSpinner /></div>}
                endMessage={
                  <div className="text-center py-8">
                    <p className="text-gray-500 dark:text-gray-400">没有更多内容了</p>
                  </div>
                }
              >
                {tweets.map((tweet) => (
                  <TweetCard
                    key={tweet._id}
                    tweet={tweet}
                    onUpdate={handleTweetUpdate}
                    onDelete={handleTweetDelete}
                  />
                ))}
              </InfiniteScroll>
            ) : (
              <div className="text-center py-20">
                <SparklesIcon className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400">暂无内容</p>
                <p className="text-gray-400 dark:text-gray-500 text-sm mt-1">稍后再来看看吧</p>
              </div>
            )}
          </div>
        </div>

        {/* Right Sidebar */}
        <div className="hidden lg:block w-80 ml-8">
          <div className="sticky top-4 space-y-6">
            {/* Trending Topics */}
            {trendingTopics.length > 0 && (
              <div className="bg-gray-50 dark:bg-dark-200 rounded-lg p-4">
                <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                  <TrendingUpIcon className="w-5 h-5 mr-2" />
                  热门话题
                </h2>
                <div className="space-y-3">
                  {trendingTopics.slice(0, 5).map((topic, index) => (
                    <button
                      key={index}
                      onClick={() => navigate(`/search?q=${encodeURIComponent('#' + topic.hashtag)}`)}
                      className="block w-full text-left p-2 hover:bg-gray-100 dark:hover:bg-dark-300 rounded transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            #{topic.hashtag}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {topic.count} 条推文
                          </p>
                        </div>
                        <span className="text-xs text-gray-400 dark:text-gray-500">
                          #{index + 1}
                        </span>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Suggested Users */}
            {suggestedUsers.length > 0 && (
              <div className="bg-gray-50 dark:bg-dark-200 rounded-lg p-4">
                <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4">
                  推荐关注
                </h2>
                <div className="space-y-3">
                  {suggestedUsers.map((user) => (
                    <div key={user._id} className="flex items-center justify-between">
                      <div 
                        className="flex items-center space-x-3 flex-1 cursor-pointer"
                        onClick={() => navigate(`/${user.username}`)}
                      >
                        <img
                          src={user.avatar || '/default-avatar.png'}
                          alt={user.displayName || user.username}
                          className="w-10 h-10 rounded-full"
                        />
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-gray-900 dark:text-white truncate">
                            {user.displayName || user.username}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                            @{user.username}
                          </p>
                        </div>
                      </div>
                      <button
                        onClick={() => handleFollowUser(user._id)}
                        className={`px-4 py-1.5 rounded-full text-sm font-medium transition-colors ${
                          user.isFollowing
                            ? 'bg-gray-200 dark:bg-dark-300 text-gray-900 dark:text-white hover:bg-gray-300 dark:hover:bg-dark-400'
                            : 'bg-blue-500 text-white hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700'
                        }`}
                      >
                        {user.isFollowing ? '已关注' : '关注'}
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default Explore;