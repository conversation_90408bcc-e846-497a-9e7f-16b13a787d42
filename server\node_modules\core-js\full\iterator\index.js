'use strict';
var parent = require('../../actual/iterator');
require('../../modules/esnext.iterator.chunks');
require('../../modules/esnext.iterator.concat');
require('../../modules/esnext.iterator.range');
require('../../modules/esnext.iterator.windows');
require('../../modules/esnext.iterator.zip');
require('../../modules/esnext.iterator.zip-keyed');
// TODO: Remove from `core-js@4`
require('../../modules/esnext.iterator.as-indexed-pairs');
require('../../modules/esnext.iterator.indexed');

module.exports = parent;
