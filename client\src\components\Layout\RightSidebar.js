import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import axios from 'axios';
import LoadingSpinner from '../UI/LoadingSpinner';
import { useAuth } from '../../contexts/AuthContext';
import toast from 'react-hot-toast';

function RightSidebar() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [trendingTopics, setTrendingTopics] = useState([]);
  const [suggestedUsers, setSuggestedUsers] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch trending topics and suggested users
  useEffect(() => {
    fetchTrendingAndSuggestions();
  }, []);

  const fetchTrendingAndSuggestions = async () => {
    try {
      const [trendingResponse, usersResponse] = await Promise.all([
        axios.get('/search/trending'),
        axios.get('/users/suggestions?limit=3')
      ]);

      setTrendingTopics(trendingResponse.data.hashtags || []);
      setSuggestedUsers(usersResponse.data.users || []);
    } catch (error) {
      console.error('Failed to fetch trending and suggestions:', error);
    } finally {
      setLoading(false);
    }
  };

  // Search functionality
  const handleSearch = async (query) => {
    if (!query.trim()) {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    setSearchLoading(true);
    try {
      const response = await axios.get('/search/suggestions', {
        params: { q: query, limit: 5 }
      });
      setSearchResults(response.data.suggestions || []);
      setShowSearchResults(true);
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setSearchLoading(false);
    }
  };

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      handleSearch(searchQuery);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
      setSearchQuery('');
      setShowSearchResults(false);
    }
  };

  const handleFollowUser = async (userId) => {
    try {
      await axios.post(`/users/${userId}/follow`);
      setSuggestedUsers(prev => 
        prev.map(user => 
          user._id === userId 
            ? { ...user, isFollowing: true, followersCount: user.followersCount + 1 }
            : user
        )
      );
      toast.success('关注成功');
    } catch (error) {
      toast.error('关注失败，请重试');
    }
  };

  const formatTweetCount = (count) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  if (loading) {
    return (
      <div className="p-4">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="p-4 space-y-4">
      {/* Search Box */}
      <div className="relative">
        <form onSubmit={handleSearchSubmit}>
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="搜索 Twitter"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onFocus={() => searchQuery && setShowSearchResults(true)}
              onBlur={() => setTimeout(() => setShowSearchResults(false), 200)}
              className="w-full pl-10 pr-4 py-3 bg-gray-100 dark:bg-dark-300 border-0 rounded-full focus:outline-none focus:ring-2 focus:ring-twitter-blue focus:bg-white dark:focus:bg-dark-200 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all"
            />
            {searchLoading && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <LoadingSpinner size="sm" />
              </div>
            )}
          </div>
        </form>

        {/* Search Results Dropdown */}
        {showSearchResults && searchResults.length > 0 && (
          <div className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-dark-200 border border-gray-200 dark:border-dark-300 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto">
            {searchResults.map((result, index) => (
              <Link
                key={index}
                to={result.type === 'user' ? `/${result.username}` : `/search?q=${encodeURIComponent(result.text)}`}
                className="block px-4 py-3 hover:bg-gray-50 dark:hover:bg-dark-300 border-b border-gray-100 dark:border-dark-400 last:border-b-0"
                onClick={() => {
                  setSearchQuery('');
                  setShowSearchResults(false);
                }}
              >
                <div className="flex items-center space-x-3">
                  {result.type === 'user' ? (
                    <>
                      <img
                        src={result.avatar || '/default-avatar.png'}
                        alt={result.displayName}
                        className="w-8 h-8 rounded-full"
                      />
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">
                          {result.displayName}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          @{result.username}
                        </div>
                      </div>
                    </>
                  ) : (
                    <>
                      <MagnifyingGlassIcon className="w-5 h-5 text-gray-400" />
                      <div className="text-gray-900 dark:text-white">
                        {result.text}
                      </div>
                    </>
                  )}
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>

      {/* Trending Topics */}
      {trendingTopics.length > 0 && (
        <div className="card p-4">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
            趋势话题
          </h2>
          <div className="space-y-3">
            {trendingTopics.slice(0, 5).map((topic, index) => (
              <Link
                key={topic._id}
                to={`/hashtag/${topic._id.replace('#', '')}`}
                className="block hover:bg-gray-50 dark:hover:bg-dark-300 p-2 rounded-lg transition-colors"
              >
                <div className="flex justify-between items-start">
                  <div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {index + 1} · 趋势
                    </div>
                    <div className="font-semibold text-gray-900 dark:text-white">
                      #{topic._id}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {formatTweetCount(topic.count)} 推文
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
          <Link
            to="/explore"
            className="block mt-4 text-twitter-blue hover:underline text-sm"
          >
            显示更多
          </Link>
        </div>
      )}

      {/* Suggested Users */}
      {suggestedUsers.length > 0 && (
        <div className="card p-4">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
            推荐关注
          </h2>
          <div className="space-y-4">
            {suggestedUsers.map((suggestedUser) => (
              <div key={suggestedUser._id} className="flex items-center justify-between">
                <Link
                  to={`/${suggestedUser.username}`}
                  className="flex items-center space-x-3 flex-1 hover:bg-gray-50 dark:hover:bg-dark-300 p-2 rounded-lg transition-colors"
                >
                  <img
                    src={suggestedUser.avatar || '/default-avatar.png'}
                    alt={suggestedUser.displayName || suggestedUser.username}
                    className="w-10 h-10 rounded-full"
                  />
                  <div className="flex-1 min-w-0">
                    <div className="font-semibold text-gray-900 dark:text-white truncate">
                      {suggestedUser.displayName || suggestedUser.username}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400 truncate">
                      @{suggestedUser.username}
                    </div>
                    {suggestedUser.bio && (
                      <div className="text-sm text-gray-600 dark:text-gray-300 truncate">
                        {suggestedUser.bio}
                      </div>
                    )}
                  </div>
                </Link>
                {suggestedUser._id !== user?._id && !suggestedUser.isFollowing && (
                  <button
                    onClick={() => handleFollowUser(suggestedUser._id)}
                    className="btn-primary px-4 py-1.5 text-sm ml-2"
                  >
                    关注
                  </button>
                )}
              </div>
            ))}
          </div>
          <Link
            to="/explore"
            className="block mt-4 text-twitter-blue hover:underline text-sm"
          >
            显示更多
          </Link>
        </div>
      )}

      {/* Footer Links */}
      <div className="text-xs text-gray-500 dark:text-gray-400 space-y-2">
        <div className="flex flex-wrap gap-2">
          <a href="#" className="hover:underline">服务条款</a>
          <a href="#" className="hover:underline">隐私政策</a>
          <a href="#" className="hover:underline">Cookie 政策</a>
        </div>
        <div className="flex flex-wrap gap-2">
          <a href="#" className="hover:underline">无障碍功能</a>
          <a href="#" className="hover:underline">广告信息</a>
          <a href="#" className="hover:underline">更多</a>
        </div>
        <div>
          © 2024 Twitter Clone
        </div>
      </div>
    </div>
  );
}

export default RightSidebar;