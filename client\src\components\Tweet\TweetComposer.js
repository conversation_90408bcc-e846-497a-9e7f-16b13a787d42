import React, { useState, useRef, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useSocket } from '../../contexts/SocketContext';
import TextareaAutosize from 'react-textarea-autosize';
import {
  PhotoIcon,
  FaceSmileIcon,
  MapPinIcon,
  GifIcon,
  CalendarDaysIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import axios from 'axios';
import toast from 'react-hot-toast';
import { ButtonLoading } from '../UI/LoadingSpinner';

function TweetComposer({ 
  onSuccess, 
  replyTo = null, 
  quoteTweet = null, 
  autoFocus = false,
  placeholder = "有什么新鲜事？"
}) {
  const { user } = useAuth();
  const { emit } = useSocket();
  const [content, setContent] = useState('');
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [charCount, setCharCount] = useState(0);
  const textareaRef = useRef(null);
  const fileInputRef = useRef(null);
  const maxChars = 280;

  useEffect(() => {
    setCharCount(content.length);
  }, [content]);

  useEffect(() => {
    if (autoFocus && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [autoFocus]);

  const handleImageUpload = (e) => {
    const files = Array.from(e.target.files);
    const maxFiles = 4;
    
    if (images.length + files.length > maxFiles) {
      toast.error(`最多只能上传 ${maxFiles} 张图片`);
      return;
    }

    files.forEach(file => {
      if (!file.type.startsWith('image/')) {
        toast.error('只能上传图片文件');
        return;
      }

      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast.error('图片大小不能超过 5MB');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        setImages(prev => [...prev, {
          file,
          preview: e.target.result,
          id: Date.now() + Math.random()
        }]);
      };
      reader.readAsDataURL(file);
    });

    // Reset file input
    e.target.value = '';
  };

  const removeImage = (imageId) => {
    setImages(prev => prev.filter(img => img.id !== imageId));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!content.trim() && images.length === 0) {
      toast.error('请输入内容或添加图片');
      return;
    }

    if (content.length > maxChars) {
      toast.error(`内容不能超过 ${maxChars} 个字符`);
      return;
    }

    setLoading(true);

    try {
      const formData = new FormData();
      formData.append('content', content.trim());
      
      if (replyTo) {
        formData.append('replyTo', replyTo._id);
      }
      
      if (quoteTweet) {
        formData.append('quoteTweet', quoteTweet._id);
      }

      // Add images
      images.forEach((image, index) => {
        formData.append('images', image.file);
      });

      const response = await axios.post('/tweets', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      // Emit real-time event
      emit('new_tweet', response.data.tweet);

      // Reset form
      setContent('');
      setImages([]);
      
      toast.success(replyTo ? '回复发送成功' : '推文发布成功');
      
      if (onSuccess) {
        onSuccess(response.data.tweet);
      }
    } catch (error) {
      console.error('Tweet creation failed:', error);
      const message = error.response?.data?.message || '发布失败，请重试';
      toast.error(message);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      handleSubmit(e);
    }
  };

  const getCharCountColor = () => {
    const percentage = (charCount / maxChars) * 100;
    if (percentage >= 100) return 'text-red-500';
    if (percentage >= 80) return 'text-yellow-500';
    return 'text-gray-500';
  };

  const isOverLimit = charCount > maxChars;
  const canTweet = (content.trim() || images.length > 0) && !isOverLimit && !loading;

  return (
    <div className="border-b border-gray-200 dark:border-dark-300">
      {/* Reply/Quote Context */}
      {replyTo && (
        <div className="px-4 pt-4 pb-2">
          <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">
            回复给 <span className="text-twitter-blue">@{replyTo.author.username}</span>
          </div>
        </div>
      )}

      {quoteTweet && (
        <div className="px-4 pt-4 pb-2">
          <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">
            引用推文
          </div>
          <div className="border border-gray-200 dark:border-dark-300 rounded-lg p-3 bg-gray-50 dark:bg-dark-300">
            <div className="flex items-center space-x-2 mb-2">
              <img
                src={quoteTweet.author.avatar || '/default-avatar.png'}
                alt={quoteTweet.author.displayName}
                className="w-5 h-5 rounded-full"
              />
              <span className="font-medium text-sm text-gray-900 dark:text-white">
                {quoteTweet.author.displayName || quoteTweet.author.username}
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                @{quoteTweet.author.username}
              </span>
            </div>
            <p className="text-sm text-gray-900 dark:text-white">
              {quoteTweet.content}
            </p>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="flex space-x-3 p-4">
          {/* User Avatar */}
          <img
            src={user?.avatar || '/default-avatar.png'}
            alt={user?.displayName || user?.username}
            className="w-10 h-10 rounded-full flex-shrink-0"
          />

          {/* Content Area */}
          <div className="flex-1">
            {/* Text Input */}
            <TextareaAutosize
              ref={textareaRef}
              value={content}
              onChange={(e) => setContent(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              className="w-full text-xl placeholder-gray-500 dark:placeholder-gray-400 bg-transparent border-0 resize-none focus:outline-none text-gray-900 dark:text-white"
              minRows={replyTo ? 2 : 3}
              maxRows={10}
            />

            {/* Image Previews */}
            {images.length > 0 && (
              <div className={`mt-3 grid gap-2 ${
                images.length === 1 ? 'grid-cols-1' :
                images.length === 2 ? 'grid-cols-2' :
                'grid-cols-2'
              }`}>
                {images.map((image) => (
                  <div key={image.id} className="relative group">
                    <img
                      src={image.preview}
                      alt="Upload preview"
                      className="w-full h-32 object-cover rounded-lg"
                    />
                    <button
                      type="button"
                      onClick={() => removeImage(image.id)}
                      className="absolute top-2 right-2 w-6 h-6 bg-black bg-opacity-60 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <XMarkIcon className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Actions Bar */}
            <div className="flex items-center justify-between mt-4">
              <div className="flex items-center space-x-4">
                {/* Image Upload */}
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={images.length >= 4}
                  className="p-2 text-twitter-blue hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-full transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  title="添加图片"
                >
                  <PhotoIcon className="w-5 h-5" />
                </button>

                {/* Other Actions (Placeholder) */}
                <button
                  type="button"
                  className="p-2 text-twitter-blue hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-full transition-colors opacity-50 cursor-not-allowed"
                  title="添加 GIF"
                  disabled
                >
                  <GifIcon className="w-5 h-5" />
                </button>
                
                <button
                  type="button"
                  className="p-2 text-twitter-blue hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-full transition-colors opacity-50 cursor-not-allowed"
                  title="添加表情"
                  disabled
                >
                  <FaceSmileIcon className="w-5 h-5" />
                </button>
                
                <button
                  type="button"
                  className="p-2 text-twitter-blue hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-full transition-colors opacity-50 cursor-not-allowed"
                  title="添加位置"
                  disabled
                >
                  <MapPinIcon className="w-5 h-5" />
                </button>
              </div>

              <div className="flex items-center space-x-3">
                {/* Character Count */}
                {content && (
                  <div className="flex items-center space-x-2">
                    <span className={`text-sm ${getCharCountColor()}`}>
                      {charCount}/{maxChars}
                    </span>
                    {charCount > 0 && (
                      <div className="w-8 h-8 relative">
                        <svg className="w-8 h-8 transform -rotate-90" viewBox="0 0 32 32">
                          <circle
                            cx="16"
                            cy="16"
                            r="14"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            className="text-gray-200 dark:text-dark-400"
                          />
                          <circle
                            cx="16"
                            cy="16"
                            r="14"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeDasharray={`${2 * Math.PI * 14}`}
                            strokeDashoffset={`${2 * Math.PI * 14 * (1 - Math.min(charCount / maxChars, 1))}`}
                            className={isOverLimit ? 'text-red-500' : 'text-twitter-blue'}
                            strokeLinecap="round"
                          />
                        </svg>
                      </div>
                    )}
                  </div>
                )}

                {/* Submit Button */}
                <ButtonLoading
                  type="submit"
                  loading={loading}
                  disabled={!canTweet}
                  className="btn-primary px-6 py-2"
                >
                  {replyTo ? '回复' : quoteTweet ? '引用推文' : '发推文'}
                </ButtonLoading>
              </div>
            </div>
          </div>
        </div>

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          multiple
          onChange={handleImageUpload}
          className="hidden"
        />
      </form>
    </div>
  );
}

export default TweetComposer;