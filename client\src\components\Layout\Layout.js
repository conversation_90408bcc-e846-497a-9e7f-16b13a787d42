import React, { useState } from 'react';
import { Outlet } from 'react-router-dom';
import Sidebar from './Sidebar';
import RightSidebar from './RightSidebar';
import MobileNavigation from './MobileNavigation';
import TweetComposer from '../Tweet/TweetComposer';
import { useAuth } from '../../contexts/AuthContext';

function Layout() {
  const { user } = useAuth();
  const [showComposer, setShowComposer] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-dark-100">
      <div className="max-w-7xl mx-auto flex">
        {/* Left Sidebar */}
        <div className={`hidden lg:block ${sidebarCollapsed ? 'w-20' : 'w-64'} transition-all duration-300`}>
          <div className="fixed h-full">
            <Sidebar 
              collapsed={sidebarCollapsed}
              onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
              onCompose={() => setShowComposer(true)}
            />
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 min-h-screen">
          <div className="max-w-2xl mx-auto border-x border-gray-200 dark:border-dark-300 min-h-screen bg-white dark:bg-dark-200">
            {/* Mobile Header */}
            <div className="lg:hidden sticky top-0 z-10 bg-white dark:bg-dark-200 border-b border-gray-200 dark:border-dark-300 backdrop-blur-md bg-opacity-80 dark:bg-opacity-80">
              <div className="flex items-center justify-between p-4">
                <div className="flex items-center space-x-3">
                  <img
                    src={user?.avatar || '/default-avatar.png'}
                    alt={user?.displayName || user?.username}
                    className="w-8 h-8 rounded-full"
                  />
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                    首页
                  </h1>
                </div>
                <button
                  onClick={() => setShowComposer(true)}
                  className="btn-primary p-2 rounded-full"
                  aria-label="写推文"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Page Content */}
            <main className="pb-20 lg:pb-0">
              <Outlet />
            </main>
          </div>
        </div>

        {/* Right Sidebar */}
        <div className="hidden xl:block w-80">
          <div className="fixed h-full w-80 overflow-y-auto">
            <RightSidebar />
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className="lg:hidden">
        <MobileNavigation onCompose={() => setShowComposer(true)} />
      </div>

      {/* Tweet Composer Modal */}
      {showComposer && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="modal-overlay" onClick={() => setShowComposer(false)}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
              <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-dark-300">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  写推文
                </h2>
                <button
                  onClick={() => setShowComposer(false)}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-dark-300 rounded-full transition-colors"
                  aria-label="关闭"
                >
                  <svg className="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
              <div className="p-4">
                <TweetComposer
                  onSuccess={() => setShowComposer(false)}
                  autoFocus
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Backdrop for mobile sidebar */}
      {sidebarCollapsed && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarCollapsed(false)}
        />
      )}
    </div>
  );
}

export default Layout;