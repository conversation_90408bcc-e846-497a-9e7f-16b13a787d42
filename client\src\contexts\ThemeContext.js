import React, { createContext, useContext, useReducer, useEffect } from 'react';
import {
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon
} from '@heroicons/react/24/outline';

const ThemeContext = createContext();

// Action types
const THEME_ACTIONS = {
  TOGGLE_THEME: 'TOGGLE_THEME',
  SET_THEME: 'SET_THEME',
  SET_SYSTEM_THEME: 'SET_SYSTEM_THEME'
};

// Theme modes
const THEME_MODES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system'
};

// Initial state
const initialState = {
  mode: THEME_MODES.SYSTEM, // 'light', 'dark', or 'system'
  darkMode: false, // actual dark mode state
  systemPrefersDark: false
};

// Reducer
function themeReducer(state, action) {
  switch (action.type) {
    case THEME_ACTIONS.TOGGLE_THEME:
      const newMode = state.darkMode ? THEME_MODES.LIGHT : THEME_MODES.DARK;
      return {
        ...state,
        mode: newMode,
        darkMode: newMode === THEME_MODES.DARK
      };
    case THEME_ACTIONS.SET_THEME:
      return {
        ...state,
        mode: action.payload,
        darkMode: action.payload === THEME_MODES.DARK || 
                 (action.payload === THEME_MODES.SYSTEM && state.systemPrefersDark)
      };
    case THEME_ACTIONS.SET_SYSTEM_THEME:
      return {
        ...state,
        systemPrefersDark: action.payload,
        darkMode: state.mode === THEME_MODES.SYSTEM ? action.payload : state.darkMode
      };
    default:
      return state;
  }
}

export function ThemeProvider({ children }) {
  const [state, dispatch] = useReducer(themeReducer, initialState);

  // Initialize theme from localStorage and system preference
  useEffect(() => {
    // Get saved theme preference
    const savedTheme = localStorage.getItem('theme');
    
    // Get system preference
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const systemPrefersDark = mediaQuery.matches;
    
    // Set system preference
    dispatch({ type: THEME_ACTIONS.SET_SYSTEM_THEME, payload: systemPrefersDark });
    
    // Set theme mode
    if (savedTheme && Object.values(THEME_MODES).includes(savedTheme)) {
      dispatch({ type: THEME_ACTIONS.SET_THEME, payload: savedTheme });
    } else {
      dispatch({ type: THEME_ACTIONS.SET_THEME, payload: THEME_MODES.SYSTEM });
    }

    // Listen for system theme changes
    const handleSystemThemeChange = (e) => {
      dispatch({ type: THEME_ACTIONS.SET_SYSTEM_THEME, payload: e.matches });
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);

    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  }, []);

  // Save theme preference to localStorage
  useEffect(() => {
    localStorage.setItem('theme', state.mode);
  }, [state.mode]);

  // Toggle between light and dark mode
  const toggleTheme = () => {
    dispatch({ type: THEME_ACTIONS.TOGGLE_THEME });
  };

  // Set specific theme mode
  const setTheme = (mode) => {
    if (Object.values(THEME_MODES).includes(mode)) {
      dispatch({ type: THEME_ACTIONS.SET_THEME, payload: mode });
    }
  };

  // Set light theme
  const setLightTheme = () => {
    setTheme(THEME_MODES.LIGHT);
  };

  // Set dark theme
  const setDarkTheme = () => {
    setTheme(THEME_MODES.DARK);
  };

  // Set system theme
  const setSystemTheme = () => {
    setTheme(THEME_MODES.SYSTEM);
  };

  // Get theme display name
  const getThemeDisplayName = (mode = state.mode) => {
    switch (mode) {
      case THEME_MODES.LIGHT:
        return '浅色模式';
      case THEME_MODES.DARK:
        return '深色模式';
      case THEME_MODES.SYSTEM:
        return '跟随系统';
      default:
        return '未知';
    }
  };

  // Get current effective theme (what the user actually sees)
  const getEffectiveTheme = () => {
    if (state.mode === THEME_MODES.SYSTEM) {
      return state.systemPrefersDark ? THEME_MODES.DARK : THEME_MODES.LIGHT;
    }
    return state.mode;
  };

  // Check if current theme is dark
  const isDarkMode = () => {
    return state.darkMode;
  };

  // Check if current theme is light
  const isLightMode = () => {
    return !state.darkMode;
  };

  // Check if using system theme
  const isSystemTheme = () => {
    return state.mode === THEME_MODES.SYSTEM;
  };

  // Get theme icon (emoji)
  const getThemeIconEmoji = (mode = state.mode) => {
    switch (mode) {
      case THEME_MODES.LIGHT:
        return '☀️';
      case THEME_MODES.DARK:
        return '🌙';
      case THEME_MODES.SYSTEM:
        return '💻';
      default:
        return '❓';
    }
  };

  // Get theme icon (Heroicon component)
  const getThemeIcon = (mode = state.mode) => {
    switch (mode) {
      case THEME_MODES.LIGHT:
        return SunIcon;
      case THEME_MODES.DARK:
        return MoonIcon;
      case THEME_MODES.SYSTEM:
        return ComputerDesktopIcon;
      default:
        return ComputerDesktopIcon;
    }
  };

  // Get all available themes
  const getAvailableThemes = () => {
    return [
      {
        mode: THEME_MODES.LIGHT,
        name: getThemeDisplayName(THEME_MODES.LIGHT),
        icon: getThemeIconEmoji(THEME_MODES.LIGHT),
        iconComponent: getThemeIcon(THEME_MODES.LIGHT)
      },
      {
        mode: THEME_MODES.DARK,
        name: getThemeDisplayName(THEME_MODES.DARK),
        icon: getThemeIconEmoji(THEME_MODES.DARK),
        iconComponent: getThemeIcon(THEME_MODES.DARK)
      },
      {
        mode: THEME_MODES.SYSTEM,
        name: getThemeDisplayName(THEME_MODES.SYSTEM),
        icon: getThemeIconEmoji(THEME_MODES.SYSTEM),
        iconComponent: getThemeIcon(THEME_MODES.SYSTEM)
      }
    ];
  };

  const value = {
    // State
    mode: state.mode,
    theme: state.mode, // Alias for compatibility
    darkMode: state.darkMode,
    systemPrefersDark: state.systemPrefersDark,
    
    // Actions
    toggleTheme,
    setTheme,
    setLightTheme,
    setDarkTheme,
    setSystemTheme,
    
    // Utilities
    getThemeDisplayName,
    getEffectiveTheme,
    isDarkMode,
    isLightMode,
    isSystemTheme,
    getThemeIcon,
    getThemeIconEmoji,
    getAvailableThemes,
    
    // Constants
    THEME_MODES
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

export default ThemeContext;