import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import { io } from 'socket.io-client';
import { useAuth } from './AuthContext';
import toast from 'react-hot-toast';

const SocketContext = createContext();

const SOCKET_URL = process.env.REACT_APP_SOCKET_URL || 'http://localhost:5000';

export function SocketProvider({ children }) {
  const { user, isAuthenticated } = useAuth();
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState(new Set());
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  useEffect(() => {
    if (isAuthenticated && user) {
      connectSocket();
    } else {
      disconnectSocket();
    }

    return () => {
      disconnectSocket();
    };
  }, [isAuthenticated, user]);

  const connectSocket = () => {
    if (socket?.connected) return;

    const token = localStorage.getItem('token');
    if (!token) return;

    const newSocket = io(SOCKET_URL, {
      auth: {
        token
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true
    });

    // Connection events
    newSocket.on('connect', () => {
      console.log('Socket connected:', newSocket.id);
      setIsConnected(true);
      reconnectAttempts.current = 0;
      
      // Join user's personal room
      if (user) {
        newSocket.emit('join', user._id);
      }
    });

    newSocket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      setIsConnected(false);
      
      // Auto-reconnect on certain disconnect reasons
      if (reason === 'io server disconnect' || reason === 'transport close') {
        if (reconnectAttempts.current < maxReconnectAttempts) {
          setTimeout(() => {
            reconnectAttempts.current++;
            connectSocket();
          }, Math.pow(2, reconnectAttempts.current) * 1000); // Exponential backoff
        }
      }
    });

    newSocket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      setIsConnected(false);
    });

    // Real-time events
    newSocket.on('notification', (notification) => {
      handleNotification(notification);
    });

    newSocket.on('new_tweet', (tweet) => {
      handleNewTweet(tweet);
    });

    newSocket.on('tweet_liked', (data) => {
      handleTweetLiked(data);
    });

    newSocket.on('tweet_retweeted', (data) => {
      handleTweetRetweeted(data);
    });

    newSocket.on('new_follower', (data) => {
      handleNewFollower(data);
    });

    newSocket.on('new_message', (message) => {
      handleNewMessage(message);
    });

    newSocket.on('user_online', (userId) => {
      setOnlineUsers(prev => new Set([...prev, userId]));
    });

    newSocket.on('user_offline', (userId) => {
      setOnlineUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(userId);
        return newSet;
      });
    });

    newSocket.on('online_users', (users) => {
      setOnlineUsers(new Set(users));
    });

    setSocket(newSocket);
  };

  const disconnectSocket = () => {
    if (socket) {
      socket.disconnect();
      setSocket(null);
      setIsConnected(false);
      setOnlineUsers(new Set());
    }
  };

  // Event handlers
  const handleNotification = (notification) => {
    // Show toast notification based on type
    switch (notification.type) {
      case 'like':
        if (notification.sender._id !== user?._id) {
          toast.success(`${notification.sender.displayName || notification.sender.username} 赞了你的推文`);
        }
        break;
      case 'retweet':
        if (notification.sender._id !== user?._id) {
          toast.success(`${notification.sender.displayName || notification.sender.username} 转发了你的推文`);
        }
        break;
      case 'reply':
        if (notification.sender._id !== user?._id) {
          toast.success(`${notification.sender.displayName || notification.sender.username} 回复了你的推文`);
        }
        break;
      case 'mention':
        if (notification.sender._id !== user?._id) {
          toast.success(`${notification.sender.displayName || notification.sender.username} 在推文中提到了你`);
        }
        break;
      case 'follow':
        if (notification.sender._id !== user?._id) {
          toast.success(`${notification.sender.displayName || notification.sender.username} 关注了你`);
        }
        break;
      case 'quote':
        if (notification.sender._id !== user?._id) {
          toast.success(`${notification.sender.displayName || notification.sender.username} 引用了你的推文`);
        }
        break;
      default:
        break;
    }

    // Dispatch custom event for components to listen
    window.dispatchEvent(new CustomEvent('newNotification', {
      detail: notification
    }));
  };

  const handleNewTweet = (tweet) => {
    // Dispatch custom event for timeline updates
    window.dispatchEvent(new CustomEvent('newTweet', {
      detail: tweet
    }));
  };

  const handleTweetLiked = (data) => {
    // Dispatch custom event for real-time like updates
    window.dispatchEvent(new CustomEvent('tweetLiked', {
      detail: data
    }));
  };

  const handleTweetRetweeted = (data) => {
    // Dispatch custom event for real-time retweet updates
    window.dispatchEvent(new CustomEvent('tweetRetweeted', {
      detail: data
    }));
  };

  const handleNewFollower = (data) => {
    // Dispatch custom event for follower updates
    window.dispatchEvent(new CustomEvent('newFollower', {
      detail: data
    }));
  };

  const handleNewMessage = (message) => {
    // Show toast for new messages
    if (message.sender._id !== user?._id) {
      toast.success(`来自 ${message.sender.displayName || message.sender.username} 的新消息`);
    }

    // Dispatch custom event for message updates
    window.dispatchEvent(new CustomEvent('newMessage', {
      detail: message
    }));
  };

  // Socket utility functions
  const emit = (event, data) => {
    if (socket && isConnected) {
      socket.emit(event, data);
    }
  };

  const on = (event, callback) => {
    if (socket) {
      socket.on(event, callback);
    }
  };

  const off = (event, callback) => {
    if (socket) {
      socket.off(event, callback);
    }
  };

  // Check if user is online
  const isUserOnline = (userId) => {
    return onlineUsers.has(userId);
  };

  // Join a room (for group chats, etc.)
  const joinRoom = (roomId) => {
    emit('join_room', roomId);
  };

  // Leave a room
  const leaveRoom = (roomId) => {
    emit('leave_room', roomId);
  };

  // Send typing indicator
  const sendTyping = (roomId, isTyping) => {
    emit('typing', { roomId, isTyping });
  };

  // Mark messages as read
  const markMessagesAsRead = (conversationId) => {
    emit('mark_messages_read', conversationId);
  };

  const value = {
    socket,
    isConnected,
    onlineUsers,
    emit,
    on,
    off,
    isUserOnline,
    joinRoom,
    leaveRoom,
    sendTyping,
    markMessagesAsRead,
    connectSocket,
    disconnectSocket
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
}

export function useSocket() {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
}

export default SocketContext;