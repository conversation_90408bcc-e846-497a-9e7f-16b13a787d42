import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useSocket } from '../contexts/SocketContext';
import axios from 'axios';
import toast from 'react-hot-toast';
import moment from 'moment';
import InfiniteScroll from 'react-infinite-scroll-component';
import {
  HeartIcon,
  ArrowPathRoundedSquareIcon,
  ChatBubbleLeftIcon,
  UserPlusIcon,
  BellIcon,
  CheckIcon,
  TrashIcon,
  EllipsisHorizontalIcon
} from '@heroicons/react/24/outline';
import {
  HeartIcon as HeartSolidIcon
} from '@heroicons/react/24/solid';
import LoadingSpinner from '../components/UI/LoadingSpinner';

function Notifications() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { socket } = useSocket();
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [filter, setFilter] = useState('all'); // all, mentions, follows, likes, retweets
  const [unreadCount, setUnreadCount] = useState(0);
  const [showDropdown, setShowDropdown] = useState(false);

  useEffect(() => {
    fetchNotifications(true);
    markAllAsRead();
  }, [filter]);

  useEffect(() => {
    if (socket) {
      // Listen for new notifications
      socket.on('notification', (notification) => {
        setNotifications(prev => [notification, ...prev]);
        setUnreadCount(prev => prev + 1);
        
        // Show toast for new notification
        const message = getNotificationMessage(notification);
        toast.success(message, {
          duration: 3000,
          icon: getNotificationIcon(notification.type)
        });
      });

      return () => {
        socket.off('notification');
      };
    }
  }, [socket]);

  const fetchNotifications = async (reset = false) => {
    try {
      setLoading(reset);
      const currentPage = reset ? 1 : page;
      const params = {
        page: currentPage,
        limit: 20,
        type: filter === 'all' ? undefined : filter
      };

      const response = await axios.get('/api/notifications', { params });
      const newNotifications = response.data.notifications;

      if (reset) {
        setNotifications(newNotifications);
        setPage(2);
      } else {
        setNotifications(prev => [...prev, ...newNotifications]);
        setPage(prev => prev + 1);
      }

      setHasMore(newNotifications.length === 20);
      setUnreadCount(response.data.unreadCount || 0);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      toast.error('获取通知失败');
    } finally {
      setLoading(false);
    }
  };

  const markAllAsRead = async () => {
    try {
      await axios.put('/api/notifications/mark-read');
      setNotifications(prev => prev.map(notif => ({ ...notif, read: true })));
      setUnreadCount(0);
      
      // Dispatch custom event to update unread count in other components
      window.dispatchEvent(new CustomEvent('notificationsRead'));
    } catch (error) {
      console.error('Error marking notifications as read:', error);
    }
  };

  const markAsRead = async (notificationId) => {
    try {
      await axios.put(`/api/notifications/${notificationId}/read`);
      setNotifications(prev => prev.map(notif => 
        notif._id === notificationId ? { ...notif, read: true } : notif
      ));
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const deleteNotification = async (notificationId) => {
    try {
      await axios.delete(`/api/notifications/${notificationId}`);
      setNotifications(prev => prev.filter(notif => notif._id !== notificationId));
      toast.success('通知已删除');
    } catch (error) {
      console.error('Error deleting notification:', error);
      toast.error('删除通知失败');
    }
  };

  const clearAllNotifications = async () => {
    try {
      await axios.delete('/api/notifications');
      setNotifications([]);
      setUnreadCount(0);
      toast.success('所有通知已清除');
      setShowDropdown(false);
    } catch (error) {
      console.error('Error clearing notifications:', error);
      toast.error('清除通知失败');
    }
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'like':
        return <HeartSolidIcon className="h-5 w-5 text-red-500" />;
      case 'retweet':
        return <ArrowPathRoundedSquareIcon className="h-5 w-5 text-green-500" />;
      case 'reply':
      case 'mention':
        return <ChatBubbleLeftIcon className="h-5 w-5 text-blue-500" />;
      case 'follow':
        return <UserPlusIcon className="h-5 w-5 text-purple-500" />;
      default:
        return <BellIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getNotificationMessage = (notification) => {
    const { type, fromUser } = notification;
    const name = fromUser?.displayName || '某用户';
    
    switch (type) {
      case 'like':
        return `${name} 喜欢了您的推文`;
      case 'retweet':
        return `${name} 转发了您的推文`;
      case 'reply':
        return `${name} 回复了您的推文`;
      case 'mention':
        return `${name} 在推文中提到了您`;
      case 'follow':
        return `${name} 关注了您`;
      default:
        return '您有新的通知';
    }
  };

  const handleNotificationClick = (notification) => {
    if (!notification.read) {
      markAsRead(notification._id);
    }

    // Navigate based on notification type
    switch (notification.type) {
      case 'like':
      case 'retweet':
      case 'reply':
      case 'mention':
        if (notification.tweet) {
          navigate(`/tweet/${notification.tweet._id}`);
        }
        break;
      case 'follow':
        if (notification.fromUser) {
          navigate(`/profile/${notification.fromUser.username}`);
        }
        break;
      default:
        break;
    }
  };

  const getNotificationContent = (notification) => {
    const { type, fromUser, tweet } = notification;
    
    switch (type) {
      case 'like':
        return (
          <div>
            <p className="text-sm text-gray-900 dark:text-white">
              <span className="font-medium">{fromUser?.displayName}</span> 喜欢了您的推文
            </p>
            {tweet && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                {tweet.content}
              </p>
            )}
          </div>
        );
      case 'retweet':
        return (
          <div>
            <p className="text-sm text-gray-900 dark:text-white">
              <span className="font-medium">{fromUser?.displayName}</span> 转发了您的推文
            </p>
            {tweet && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                {tweet.content}
              </p>
            )}
          </div>
        );
      case 'reply':
        return (
          <div>
            <p className="text-sm text-gray-900 dark:text-white">
              <span className="font-medium">{fromUser?.displayName}</span> 回复了您的推文
            </p>
            {tweet && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                {tweet.content}
              </p>
            )}
          </div>
        );
      case 'mention':
        return (
          <div>
            <p className="text-sm text-gray-900 dark:text-white">
              <span className="font-medium">{fromUser?.displayName}</span> 在推文中提到了您
            </p>
            {tweet && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                {tweet.content}
              </p>
            )}
          </div>
        );
      case 'follow':
        return (
          <div>
            <p className="text-sm text-gray-900 dark:text-white">
              <span className="font-medium">{fromUser?.displayName}</span> 关注了您
            </p>
            {fromUser?.bio && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-1">
                {fromUser.bio}
              </p>
            )}
          </div>
        );
      default:
        return (
          <p className="text-sm text-gray-900 dark:text-white">
            您有新的通知
          </p>
        );
    }
  };

  const filters = [
    { id: 'all', label: '全部' },
    { id: 'mentions', label: '提及' },
    { id: 'follows', label: '关注' },
    { id: 'likes', label: '喜欢' },
    { id: 'retweets', label: '转发' }
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-dark-100">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white/80 dark:bg-dark-100/80 backdrop-blur-md border-b border-gray-200 dark:border-dark-300">
        <div className="max-w-2xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                通知
              </h1>
              {unreadCount > 0 && (
                <span className="bg-blue-500 text-white text-xs font-medium px-2 py-1 rounded-full">
                  {unreadCount}
                </span>
              )}
            </div>
            <div className="relative">
              <button
                onClick={() => setShowDropdown(!showDropdown)}
                className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-dark-200 transition-colors"
              >
                <EllipsisHorizontalIcon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
              </button>
              {showDropdown && (
                <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-dark-200 rounded-lg shadow-lg border border-gray-200 dark:border-dark-300 py-1 z-20">
                  <button
                    onClick={() => {
                      markAllAsRead();
                      setShowDropdown(false);
                    }}
                    className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-300 flex items-center space-x-2"
                  >
                    <CheckIcon className="h-4 w-4" />
                    <span>全部标为已读</span>
                  </button>
                  <button
                    onClick={clearAllNotifications}
                    className="w-full px-4 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-dark-300 flex items-center space-x-2"
                  >
                    <TrashIcon className="h-4 w-4" />
                    <span>清除所有通知</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-2xl mx-auto">
        {/* Filter Tabs */}
        <div className="border-b border-gray-200 dark:border-dark-300">
          <div className="flex overflow-x-auto">
            {filters.map((filterOption) => (
              <button
                key={filterOption.id}
                onClick={() => {
                  setFilter(filterOption.id);
                  setNotifications([]);
                  setPage(1);
                  setHasMore(true);
                }}
                className={`flex-shrink-0 py-4 px-6 text-sm font-medium transition-colors relative ${
                  filter === filterOption.id
                    ? 'text-blue-500 border-b-2 border-blue-500'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
              >
                {filterOption.label}
              </button>
            ))}
          </div>
        </div>

        {/* Notifications List */}
        <div className="pb-20">
          {loading && notifications.length === 0 ? (
            <div className="flex justify-center py-12">
              <LoadingSpinner size="lg" />
            </div>
          ) : notifications.length === 0 ? (
            <div className="text-center py-12">
              <BellIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                暂无通知
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                {filter === 'all' ? '您还没有收到任何通知' : `您还没有收到任何${filters.find(f => f.id === filter)?.label}通知`}
              </p>
            </div>
          ) : (
            <InfiniteScroll
              dataLength={notifications.length}
              next={() => fetchNotifications(false)}
              hasMore={hasMore}
              loader={
                <div className="flex justify-center py-4">
                  <LoadingSpinner size="sm" />
                </div>
              }
              endMessage={
                notifications.length > 0 && (
                  <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                    没有更多通知了
                  </div>
                )
              }
            >
              {notifications.map((notification) => (
                <div
                  key={notification._id}
                  className={`border-b border-gray-200 dark:border-dark-300 hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors cursor-pointer ${
                    !notification.read ? 'bg-blue-50 dark:bg-blue-900/10' : ''
                  }`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="p-4">
                    <div className="flex space-x-3">
                      {/* Icon */}
                      <div className="flex-shrink-0">
                        {getNotificationIcon(notification.type)}
                      </div>

                      {/* Avatar */}
                      <div className="flex-shrink-0">
                        {notification.fromUser?.avatar ? (
                          <img
                            src={notification.fromUser.avatar}
                            alt={notification.fromUser.displayName}
                            className="w-8 h-8 rounded-full"
                          />
                        ) : (
                          <div className="w-8 h-8 bg-gray-300 dark:bg-dark-300 rounded-full flex items-center justify-center">
                            <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                              {notification.fromUser?.displayName?.charAt(0).toUpperCase()}
                            </span>
                          </div>
                        )}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        {getNotificationContent(notification)}
                        <div className="flex items-center justify-between mt-2">
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {moment(notification.createdAt).fromNow()}
                          </p>
                          {!notification.read && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full" />
                          )}
                        </div>
                      </div>

                      {/* Delete Button */}
                      <div className="flex-shrink-0">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteNotification(notification._id);
                          }}
                          className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-dark-300 transition-colors opacity-0 group-hover:opacity-100"
                        >
                          <TrashIcon className="h-4 w-4 text-gray-400 hover:text-red-500" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </InfiniteScroll>
          )}
        </div>
      </div>
    </div>
  );
}

export default Notifications;