import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import toast from 'react-hot-toast';
import InfiniteScroll from 'react-infinite-scroll-component';
import {
  MagnifyingGlassIcon,
  ArrowLeftIcon,
  HashtagIcon,
  UserIcon,
  ChatBubbleOvalLeftIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline';
import TweetCard from '../components/Tweet/TweetCard';
import LoadingSpinner from '../components/UI/LoadingSpinner';

function Search() {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const [query, setQuery] = useState(searchParams.get('q') || '');
  const [activeTab, setActiveTab] = useState('all'); // all, tweets, users, hashtags
  const [results, setResults] = useState({
    tweets: [],
    users: [],
    hashtags: []
  });
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState({
    tweets: true,
    users: true,
    hashtags: true
  });
  const [page, setPage] = useState({
    tweets: 1,
    users: 1,
    hashtags: 1
  });
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    author: '',
    mentions: '',
    hashtag: '',
    dateFrom: '',
    dateTo: ''
  });

  useEffect(() => {
    const searchQuery = searchParams.get('q');
    if (searchQuery && searchQuery !== query) {
      setQuery(searchQuery);
      performSearch(searchQuery);
    }
  }, [searchParams]);

  useEffect(() => {
    if (query.trim()) {
      performSearch(query);
    }
  }, [activeTab, filters]);

  const performSearch = async (searchQuery = query, isLoadMore = false) => {
    if (!searchQuery.trim()) return;

    try {
      setLoading(!isLoadMore);
      
      const currentPage = isLoadMore ? page[activeTab] + 1 : 1;
      const params = {
        q: searchQuery,
        type: activeTab === 'all' ? undefined : activeTab,
        page: currentPage,
        limit: 20,
        ...filters
      };

      // Remove empty filter values
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === undefined) {
          delete params[key];
        }
      });

      const response = await axios.get('/search', { params });
      const data = response.data;

      if (activeTab === 'all') {
        if (isLoadMore) {
          setResults(prev => ({
            tweets: [...prev.tweets, ...(data.tweets || [])],
            users: [...prev.users, ...(data.users || [])],
            hashtags: [...prev.hashtags, ...(data.hashtags || [])]
          }));
        } else {
          setResults({
            tweets: data.tweets || [],
            users: data.users || [],
            hashtags: data.hashtags || []
          });
        }
        setHasMore({
          tweets: data.hasMore?.tweets || false,
          users: data.hasMore?.users || false,
          hashtags: data.hasMore?.hashtags || false
        });
      } else {
        const resultKey = activeTab;
        if (isLoadMore) {
          setResults(prev => ({
            ...prev,
            [resultKey]: [...prev[resultKey], ...(data[resultKey] || [])]
          }));
        } else {
          setResults(prev => ({
            ...prev,
            [resultKey]: data[resultKey] || []
          }));
        }
        setHasMore(prev => ({
          ...prev,
          [resultKey]: data.hasMore || false
        }));
      }

      if (isLoadMore) {
        setPage(prev => ({
          ...prev,
          [activeTab]: currentPage
        }));
      } else {
        setPage({
          tweets: 1,
          users: 1,
          hashtags: 1
        });
      }
    } catch (error) {
      console.error('Error performing search:', error);
      toast.error('搜索失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    if (query.trim()) {
      setSearchParams({ q: query.trim() });
      performSearch(query.trim());
    }
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  const handleLoadMore = () => {
    if (hasMore[activeTab] && !loading) {
      performSearch(query, true);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleClearFilters = () => {
    setFilters({
      author: '',
      mentions: '',
      hashtag: '',
      dateFrom: '',
      dateTo: ''
    });
  };

  const handleFollowUser = async (userId) => {
    try {
      await axios.post(`/users/${userId}/follow`);
      setResults(prev => ({
        ...prev,
        users: prev.users.map(user => 
          user._id === userId ? { ...user, isFollowing: !user.isFollowing } : user
        )
      }));
      toast.success('关注成功');
    } catch (error) {
      toast.error('操作失败，请重试');
    }
  };

  const handleTweetUpdate = (tweetId, updates) => {
    setResults(prev => ({
      ...prev,
      tweets: prev.tweets.map(tweet => 
        tweet._id === tweetId ? { ...tweet, ...updates } : tweet
      )
    }));
  };

  const handleTweetDelete = (tweetId) => {
    setResults(prev => ({
      ...prev,
      tweets: prev.tweets.filter(tweet => tweet._id !== tweetId)
    }));
  };

  const getTotalResults = () => {
    return results.tweets.length + results.users.length + results.hashtags.length;
  };

  const getTabResults = () => {
    switch (activeTab) {
      case 'tweets':
        return results.tweets;
      case 'users':
        return results.users;
      case 'hashtags':
        return results.hashtags;
      default:
        return [...results.tweets, ...results.users, ...results.hashtags];
    }
  };

  const tabs = [
    { id: 'all', label: '全部', icon: MagnifyingGlassIcon },
    { id: 'tweets', label: '推文', icon: ChatBubbleOvalLeftIcon },
    { id: 'users', label: '用户', icon: UserIcon },
    { id: 'hashtags', label: '话题', icon: HashtagIcon }
  ];

  const hasActiveFilters = Object.values(filters).some(value => value.trim() !== '');

  return (
    <div className="min-h-screen bg-white dark:bg-dark-100">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="sticky top-0 z-10 bg-white/80 dark:bg-dark-100/80 backdrop-blur-md border-b border-gray-200 dark:border-dark-300">
          <div className="flex items-center space-x-4 p-4">
            <button
              onClick={() => navigate(-1)}
              className="p-2 hover:bg-gray-100 dark:hover:bg-dark-300 rounded-full transition-colors"
            >
              <ArrowLeftIcon className="w-5 h-5 text-gray-900 dark:text-white" />
            </button>
            
            {/* Search Bar */}
            <form onSubmit={handleSearchSubmit} className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  placeholder="搜索推文、用户、话题..."
                  className="w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-dark-200 border-0 rounded-full focus:ring-2 focus:ring-blue-500 focus:bg-white dark:focus:bg-dark-100 transition-colors text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                />
              </div>
            </form>
            
            {/* Filter Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`p-2 rounded-full transition-colors ${
                hasActiveFilters || showFilters
                  ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-500'
                  : 'hover:bg-gray-100 dark:hover:bg-dark-300 text-gray-500 dark:text-gray-400'
              }`}
            >
              <AdjustmentsHorizontalIcon className="w-5 h-5" />
            </button>
          </div>
          
          {/* Advanced Filters */}
          {showFilters && (
            <div className="border-t border-gray-200 dark:border-dark-300 p-4 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    作者
                  </label>
                  <input
                    type="text"
                    value={filters.author}
                    onChange={(e) => handleFilterChange('author', e.target.value)}
                    placeholder="@username"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-dark-300 rounded-md bg-white dark:bg-dark-200 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    提及
                  </label>
                  <input
                    type="text"
                    value={filters.mentions}
                    onChange={(e) => handleFilterChange('mentions', e.target.value)}
                    placeholder="@username"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-dark-300 rounded-md bg-white dark:bg-dark-200 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    话题
                  </label>
                  <input
                    type="text"
                    value={filters.hashtag}
                    onChange={(e) => handleFilterChange('hashtag', e.target.value)}
                    placeholder="#hashtag"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-dark-300 rounded-md bg-white dark:bg-dark-200 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    日期范围
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="date"
                      value={filters.dateFrom}
                      onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-dark-300 rounded-md bg-white dark:bg-dark-200 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <input
                      type="date"
                      value={filters.dateTo}
                      onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-dark-300 rounded-md bg-white dark:bg-dark-200 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <button
                  onClick={handleClearFilters}
                  className="px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                >
                  清除筛选
                </button>
                <button
                  onClick={() => performSearch(query)}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm font-medium"
                >
                  应用筛选
                </button>
              </div>
            </div>
          )}
          
          {/* Tabs */}
          <div className="flex border-b border-gray-200 dark:border-dark-300">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              const count = activeTab === 'all' ? getTotalResults() : results[tab.id]?.length || 0;
              return (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className={`flex-1 flex items-center justify-center space-x-2 py-4 px-4 font-medium transition-colors relative ${
                    activeTab === tab.id
                      ? 'text-blue-500 dark:text-blue-400'
                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span>{tab.label}</span>
                  {count > 0 && (
                    <span className="text-xs bg-gray-200 dark:bg-dark-300 text-gray-600 dark:text-gray-400 px-2 py-0.5 rounded-full">
                      {count}
                    </span>
                  )}
                  {activeTab === tab.id && (
                    <div className="absolute bottom-0 left-0 right-0 h-1 bg-blue-500 dark:bg-blue-400 rounded-full" />
                  )}
                </button>
              );
            })}
          </div>
        </div>

        {/* Content */}
        <div>
          {loading && !results.tweets.length && !results.users.length && !results.hashtags.length ? (
            <div className="flex justify-center items-center py-20">
              <LoadingSpinner size="lg" />
            </div>
          ) : (
            <>
              {/* Results */}
              {activeTab === 'all' ? (
                <div>
                  {/* Tweets */}
                  {results.tweets.length > 0 && (
                    <div>
                      <h2 className="text-lg font-bold text-gray-900 dark:text-white p-4 border-b border-gray-200 dark:border-dark-300">
                        推文
                      </h2>
                      {results.tweets.slice(0, 3).map((tweet) => (
                        <TweetCard
                          key={tweet._id}
                          tweet={tweet}
                          onUpdate={handleTweetUpdate}
                          onDelete={handleTweetDelete}
                        />
                      ))}
                      {results.tweets.length > 3 && (
                        <div className="p-4 border-b border-gray-200 dark:border-dark-300">
                          <button
                            onClick={() => setActiveTab('tweets')}
                            className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                          >
                            查看全部 {results.tweets.length} 条推文
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* Users */}
                  {results.users.length > 0 && (
                    <div>
                      <h2 className="text-lg font-bold text-gray-900 dark:text-white p-4 border-b border-gray-200 dark:border-dark-300">
                        用户
                      </h2>
                      {results.users.slice(0, 3).map((user) => (
                        <div key={user._id} className="p-4 border-b border-gray-200 dark:border-dark-300 hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors">
                          <div className="flex items-center justify-between">
                            <div 
                              className="flex items-center space-x-3 flex-1 cursor-pointer"
                              onClick={() => navigate(`/${user.username}`)}
                            >
                              <img
                                src={user.avatar || '/default-avatar.png'}
                                alt={user.displayName || user.username}
                                className="w-12 h-12 rounded-full"
                              />
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center space-x-2">
                                  <h3 className="font-bold text-gray-900 dark:text-white truncate">
                                    {user.displayName || user.username}
                                  </h3>
                                  {user.verified && (
                                    <svg className="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                    </svg>
                                  )}
                                </div>
                                <p className="text-gray-500 dark:text-gray-400 truncate">@{user.username}</p>
                                {user.bio && (
                                  <p className="text-gray-700 dark:text-gray-300 text-sm mt-1 line-clamp-2">
                                    {user.bio}
                                  </p>
                                )}
                                <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mt-2">
                                  <span>{user.followersCount || 0} 关注者</span>
                                  <span>{user.followingCount || 0} 正在关注</span>
                                </div>
                              </div>
                            </div>
                            <button
                              onClick={() => handleFollowUser(user._id)}
                              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                                user.isFollowing
                                  ? 'bg-gray-200 dark:bg-dark-300 text-gray-900 dark:text-white hover:bg-gray-300 dark:hover:bg-dark-400'
                                  : 'bg-blue-500 text-white hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700'
                              }`}
                            >
                              {user.isFollowing ? '已关注' : '关注'}
                            </button>
                          </div>
                        </div>
                      ))}
                      {results.users.length > 3 && (
                        <div className="p-4 border-b border-gray-200 dark:border-dark-300">
                          <button
                            onClick={() => setActiveTab('users')}
                            className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                          >
                            查看全部 {results.users.length} 个用户
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* Hashtags */}
                  {results.hashtags.length > 0 && (
                    <div>
                      <h2 className="text-lg font-bold text-gray-900 dark:text-white p-4 border-b border-gray-200 dark:border-dark-300">
                        话题
                      </h2>
                      {results.hashtags.slice(0, 5).map((hashtag, index) => (
                        <button
                          key={index}
                          onClick={() => {
                            setQuery(`#${hashtag.hashtag}`);
                            setSearchParams({ q: `#${hashtag.hashtag}` });
                          }}
                          className="w-full p-4 border-b border-gray-200 dark:border-dark-300 hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors text-left"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                                <HashtagIcon className="w-5 h-5 text-blue-500" />
                              </div>
                              <div>
                                <p className="font-medium text-gray-900 dark:text-white">
                                  #{hashtag.hashtag}
                                </p>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                  {hashtag.count} 条推文
                                </p>
                              </div>
                            </div>
                          </div>
                        </button>
                      ))}
                      {results.hashtags.length > 5 && (
                        <div className="p-4">
                          <button
                            onClick={() => setActiveTab('hashtags')}
                            className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                          >
                            查看全部 {results.hashtags.length} 个话题
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ) : (
                <div>
                  {activeTab === 'tweets' && (
                    <InfiniteScroll
                      dataLength={results.tweets.length}
                      next={handleLoadMore}
                      hasMore={hasMore.tweets}
                      loader={<div className="flex justify-center py-4"><LoadingSpinner /></div>}
                      endMessage={
                        <div className="text-center py-8">
                          <p className="text-gray-500 dark:text-gray-400">没有更多推文了</p>
                        </div>
                      }
                    >
                      {results.tweets.map((tweet) => (
                        <TweetCard
                          key={tweet._id}
                          tweet={tweet}
                          onUpdate={handleTweetUpdate}
                          onDelete={handleTweetDelete}
                        />
                      ))}
                    </InfiniteScroll>
                  )}
                  
                  {activeTab === 'users' && (
                    <div>
                      {results.users.map((user) => (
                        <div key={user._id} className="p-4 border-b border-gray-200 dark:border-dark-300 hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors">
                          <div className="flex items-center justify-between">
                            <div 
                              className="flex items-center space-x-3 flex-1 cursor-pointer"
                              onClick={() => navigate(`/${user.username}`)}
                            >
                              <img
                                src={user.avatar || '/default-avatar.png'}
                                alt={user.displayName || user.username}
                                className="w-12 h-12 rounded-full"
                              />
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center space-x-2">
                                  <h3 className="font-bold text-gray-900 dark:text-white truncate">
                                    {user.displayName || user.username}
                                  </h3>
                                  {user.verified && (
                                    <svg className="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                    </svg>
                                  )}
                                </div>
                                <p className="text-gray-500 dark:text-gray-400 truncate">@{user.username}</p>
                                {user.bio && (
                                  <p className="text-gray-700 dark:text-gray-300 text-sm mt-1 line-clamp-2">
                                    {user.bio}
                                  </p>
                                )}
                                <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mt-2">
                                  <span>{user.followersCount || 0} 关注者</span>
                                  <span>{user.followingCount || 0} 正在关注</span>
                                </div>
                              </div>
                            </div>
                            <button
                              onClick={() => handleFollowUser(user._id)}
                              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                                user.isFollowing
                                  ? 'bg-gray-200 dark:bg-dark-300 text-gray-900 dark:text-white hover:bg-gray-300 dark:hover:bg-dark-400'
                                  : 'bg-blue-500 text-white hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700'
                              }`}
                            >
                              {user.isFollowing ? '已关注' : '关注'}
                            </button>
                          </div>
                        </div>
                      ))}
                      {hasMore.users && results.users.length > 0 && (
                        <div className="p-4 text-center">
                          <button
                            onClick={handleLoadMore}
                            disabled={loading}
                            className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                          >
                            {loading ? '加载中...' : '查看更多用户'}
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                  
                  {activeTab === 'hashtags' && (
                    <div>
                      {results.hashtags.map((hashtag, index) => (
                        <button
                          key={index}
                          onClick={() => {
                            setQuery(`#${hashtag.hashtag}`);
                            setSearchParams({ q: `#${hashtag.hashtag}` });
                          }}
                          className="w-full p-4 border-b border-gray-200 dark:border-dark-300 hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors text-left"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                                <HashtagIcon className="w-5 h-5 text-blue-500" />
                              </div>
                              <div>
                                <p className="font-medium text-gray-900 dark:text-white">
                                  #{hashtag.hashtag}
                                </p>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                  {hashtag.count} 条推文
                                </p>
                              </div>
                            </div>
                          </div>
                        </button>
                      ))}
                      {hasMore.hashtags && results.hashtags.length > 0 && (
                        <div className="p-4 text-center">
                          <button
                            onClick={handleLoadMore}
                            disabled={loading}
                            className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                          >
                            {loading ? '加载中...' : '查看更多话题'}
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
              
              {/* No Results */}
              {!loading && getTotalResults() === 0 && query.trim() && (
                <div className="text-center py-20">
                  <MagnifyingGlassIcon className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">没有找到相关结果</p>
                  <p className="text-gray-400 dark:text-gray-500 text-sm mt-1">
                    尝试使用不同的关键词或调整筛选条件
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export default Search;