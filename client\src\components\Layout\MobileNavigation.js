import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import {
  HomeIcon,
  MagnifyingGlassIcon,
  BellIcon,
  EnvelopeIcon,
  PencilSquareIcon
} from '@heroicons/react/24/outline';
import {
  HomeIcon as HomeIconSolid,
  MagnifyingGlassIcon as MagnifyingGlassIconSolid,
  BellIcon as BellIconSolid,
  EnvelopeIcon as EnvelopeIconSolid
} from '@heroicons/react/24/solid';

function MobileNavigation({ onCompose }) {
  const { user } = useAuth();
  const location = useLocation();
  const [unreadNotifications, setUnreadNotifications] = useState(0);
  const [unreadMessages, setUnreadMessages] = useState(0);

  // Navigation items for mobile
  const navigationItems = [
    {
      name: '首页',
      href: '/',
      icon: HomeIcon,
      iconSolid: HomeIconSolid,
      current: location.pathname === '/'
    },
    {
      name: '搜索',
      href: '/explore',
      icon: MagnifyingGlassIcon,
      iconSolid: MagnifyingGlassIconSolid,
      current: location.pathname === '/explore' || location.pathname.startsWith('/search')
    },
    {
      name: '通知',
      href: '/notifications',
      icon: BellIcon,
      iconSolid: BellIconSolid,
      current: location.pathname === '/notifications',
      badge: unreadNotifications
    },
    {
      name: '消息',
      href: '/messages',
      icon: EnvelopeIcon,
      iconSolid: EnvelopeIconSolid,
      current: location.pathname === '/messages',
      badge: unreadMessages
    }
  ];

  // Listen for notification updates
  useEffect(() => {
    const handleNewNotification = () => {
      setUnreadNotifications(prev => prev + 1);
    };

    const handleNewMessage = () => {
      setUnreadMessages(prev => prev + 1);
    };

    window.addEventListener('newNotification', handleNewNotification);
    window.addEventListener('newMessage', handleNewMessage);

    return () => {
      window.removeEventListener('newNotification', handleNewNotification);
      window.removeEventListener('newMessage', handleNewMessage);
    };
  }, []);

  // Reset notification count when visiting pages
  useEffect(() => {
    if (location.pathname === '/notifications') {
      setUnreadNotifications(0);
    }
    if (location.pathname === '/messages') {
      setUnreadMessages(0);
    }
  }, [location.pathname]);

  return (
    <>
      {/* Bottom Navigation Bar */}
      <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-dark-200 border-t border-gray-200 dark:border-dark-300 z-40">
        <div className="flex items-center justify-around py-2">
          {navigationItems.map((item) => {
            const Icon = item.current ? item.iconSolid : item.icon;
            return (
              <Link
                key={item.name}
                to={item.href}
                className="flex flex-col items-center justify-center p-2 relative"
              >
                <div className="relative">
                  <Icon className={`w-6 h-6 ${item.current ? 'text-twitter-blue' : 'text-gray-600 dark:text-gray-400'}`} />
                  {item.badge > 0 && (
                    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                      {item.badge > 99 ? '99+' : item.badge}
                    </span>
                  )}
                </div>
                <span className={`text-xs mt-1 ${item.current ? 'text-twitter-blue font-medium' : 'text-gray-600 dark:text-gray-400'}`}>
                  {item.name}
                </span>
              </Link>
            );
          })}
        </div>
      </div>

      {/* Floating Compose Button */}
      <button
        onClick={onCompose}
        className="fixed bottom-20 right-4 w-14 h-14 bg-twitter-blue hover:bg-twitter-darkBlue text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center z-50"
        aria-label="写推文"
      >
        <PencilSquareIcon className="w-6 h-6" />
      </button>

      {/* Safe area for devices with home indicator */}
      <div className="h-safe-area-inset-bottom bg-white dark:bg-dark-200" />
    </>
  );
}

export default MobileNavigation;