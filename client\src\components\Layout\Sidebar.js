import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useSocket } from '../../contexts/SocketContext';
import {
  HomeIcon,
  MagnifyingGlassIcon,
  BellIcon,
  EnvelopeIcon,
  UserIcon,
  Cog6ToothIcon,
  EllipsisHorizontalIcon,
  PencilSquareIcon,
  ArrowLeftOnRectangleIcon
} from '@heroicons/react/24/outline';
import {
  HomeIcon as HomeIconSolid,
  MagnifyingGlassIcon as MagnifyingGlassIconSolid,
  BellIcon as BellIconSolid,
  EnvelopeIcon as EnvelopeIconSolid,
  UserIcon as UserIconSolid
} from '@heroicons/react/24/solid';

function Sidebar({ collapsed, onToggleCollapse, onCompose }) {
  const { user, logout } = useAuth();
  const { isConnected } = useSocket();
  const location = useLocation();
  const navigate = useNavigate();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [unreadNotifications, setUnreadNotifications] = useState(0);
  const [unreadMessages, setUnreadMessages] = useState(0);

  // Navigation items
  const navigationItems = [
    {
      name: '首页',
      href: '/',
      icon: HomeIcon,
      iconSolid: HomeIconSolid,
      current: location.pathname === '/'
    },
    {
      name: '探索',
      href: '/explore',
      icon: MagnifyingGlassIcon,
      iconSolid: MagnifyingGlassIconSolid,
      current: location.pathname === '/explore'
    },
    {
      name: '通知',
      href: '/notifications',
      icon: BellIcon,
      iconSolid: BellIconSolid,
      current: location.pathname === '/notifications',
      badge: unreadNotifications
    },
    {
      name: '消息',
      href: '/messages',
      icon: EnvelopeIcon,
      iconSolid: EnvelopeIconSolid,
      current: location.pathname === '/messages',
      badge: unreadMessages
    },
    {
      name: '个人资料',
      href: `/${user?.username}`,
      icon: UserIcon,
      iconSolid: UserIconSolid,
      current: location.pathname === `/${user?.username}`
    },
    {
      name: '设置',
      href: '/settings',
      icon: Cog6ToothIcon,
      iconSolid: Cog6ToothIcon,
      current: location.pathname === '/settings'
    }
  ];

  // Listen for notification updates
  useEffect(() => {
    const handleNewNotification = () => {
      setUnreadNotifications(prev => prev + 1);
    };

    const handleNewMessage = () => {
      setUnreadMessages(prev => prev + 1);
    };

    window.addEventListener('newNotification', handleNewNotification);
    window.addEventListener('newMessage', handleNewMessage);

    return () => {
      window.removeEventListener('newNotification', handleNewNotification);
      window.removeEventListener('newMessage', handleNewMessage);
    };
  }, []);

  // Reset notification count when visiting notifications page
  useEffect(() => {
    if (location.pathname === '/notifications') {
      setUnreadNotifications(0);
    }
    if (location.pathname === '/messages') {
      setUnreadMessages(0);
    }
  }, [location.pathname]);

  const handleLogout = () => {
    logout();
    navigate('/auth/login');
  };

  const handleUserMenuToggle = () => {
    setShowUserMenu(!showUserMenu);
  };

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showUserMenu && !event.target.closest('.user-menu-container')) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showUserMenu]);

  return (
    <div className={`h-full bg-white dark:bg-dark-200 border-r border-gray-200 dark:border-dark-300 flex flex-col ${collapsed ? 'w-20' : 'w-64'} transition-all duration-300`}>
      {/* Logo */}
      <div className="p-4">
        <Link to="/" className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-twitter-blue rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
            </svg>
          </div>
          {!collapsed && (
            <span className="text-xl font-bold text-gray-900 dark:text-white">
              Twitter
            </span>
          )}
        </Link>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-2">
        <ul className="space-y-1">
          {navigationItems.map((item) => {
            const Icon = item.current ? item.iconSolid : item.icon;
            return (
              <li key={item.name}>
                <Link
                  to={item.href}
                  className={`sidebar-item ${item.current ? 'active' : ''} relative`}
                  title={collapsed ? item.name : undefined}
                >
                  <div className="relative">
                    <Icon className="w-6 h-6" />
                    {item.badge > 0 && (
                      <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                        {item.badge > 99 ? '99+' : item.badge}
                      </span>
                    )}
                  </div>
                  {!collapsed && (
                    <span className="text-lg">{item.name}</span>
                  )}
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Compose Button */}
      <div className="px-4 mb-4">
        <button
          onClick={onCompose}
          className={`btn-primary w-full ${collapsed ? 'p-3 rounded-full' : 'py-3 px-6 rounded-full'} font-semibold text-lg transition-all duration-200`}
        >
          {collapsed ? (
            <PencilSquareIcon className="w-6 h-6" />
          ) : (
            '写推文'
          )}
        </button>
      </div>

      {/* User Menu */}
      <div className="p-4 border-t border-gray-200 dark:border-dark-300 user-menu-container relative">
        <button
          onClick={handleUserMenuToggle}
          className="w-full flex items-center space-x-3 p-3 rounded-full hover:bg-gray-100 dark:hover:bg-dark-300 transition-colors"
        >
          <div className="relative">
            <img
              src={user?.avatar || '/default-avatar.png'}
              alt={user?.displayName || user?.username}
              className="w-10 h-10 rounded-full"
            />
            {/* Online indicator */}
            <div className={`absolute bottom-0 right-0 w-3 h-3 rounded-full border-2 border-white dark:border-dark-200 ${
              isConnected ? 'bg-green-500' : 'bg-gray-400'
            }`} />
          </div>
          {!collapsed && (
            <div className="flex-1 text-left">
              <div className="font-semibold text-gray-900 dark:text-white">
                {user?.displayName || user?.username}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                @{user?.username}
              </div>
            </div>
          )}
          {!collapsed && (
            <EllipsisHorizontalIcon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          )}
        </button>

        {/* User Dropdown Menu */}
        {showUserMenu && (
          <div className={`absolute ${collapsed ? 'left-full ml-2' : 'bottom-full mb-2'} ${collapsed ? 'bottom-0' : 'left-0'} bg-white dark:bg-dark-200 border border-gray-200 dark:border-dark-300 rounded-lg shadow-lg py-2 z-50 min-w-48`}>
            <Link
              to={`/${user?.username}`}
              className="dropdown-item"
              onClick={() => setShowUserMenu(false)}
            >
              <UserIcon className="w-4 h-4 mr-3" />
              个人资料
            </Link>
            <Link
              to="/settings"
              className="dropdown-item"
              onClick={() => setShowUserMenu(false)}
            >
              <Cog6ToothIcon className="w-4 h-4 mr-3" />
              设置
            </Link>
            <hr className="my-1 border-gray-200 dark:border-dark-300" />
            <button
              onClick={handleLogout}
              className="dropdown-item w-full text-left text-red-600 dark:text-red-400"
            >
              <ArrowLeftOnRectangleIcon className="w-4 h-4 mr-3" />
              退出登录
            </button>
          </div>
        )}
      </div>

      {/* Collapse Toggle */}
      <button
        onClick={onToggleCollapse}
        className="absolute -right-3 top-20 bg-white dark:bg-dark-200 border border-gray-200 dark:border-dark-300 rounded-full p-1.5 shadow-md hover:shadow-lg transition-shadow"
        aria-label={collapsed ? '展开侧边栏' : '收起侧边栏'}
      >
        <svg
          className={`w-4 h-4 text-gray-600 dark:text-gray-400 transition-transform ${collapsed ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
      </button>
    </div>
  );
}

export default Sidebar;