import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useSocket } from '../contexts/SocketContext';
import axios from 'axios';
import toast from 'react-hot-toast';
import moment from 'moment';
import InfiniteScroll from 'react-infinite-scroll-component';
import {
  CalendarIcon,
  LinkIcon,
  MapPinIcon,
  UserPlusIcon,
  UserMinusIcon,
  Cog6ToothIcon,
  PhotoIcon,
  EllipsisHorizontalIcon
} from '@heroicons/react/24/outline';
import {
  CheckBadgeIcon
} from '@heroicons/react/24/solid';
import TweetCard from '../components/Tweet/TweetCard';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import TweetComposer from '../components/Tweet/TweetComposer';

function Profile() {
  const { username } = useParams();
  const navigate = useNavigate();
  const { user: currentUser } = useAuth();
  const { socket } = useSocket();
  const [user, setUser] = useState(null);
  const [tweets, setTweets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [tweetsLoading, setTweetsLoading] = useState(false);
  const [following, setFollowing] = useState(false);
  const [followLoading, setFollowLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('tweets'); // tweets, replies, media, likes
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [showComposer, setShowComposer] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);

  const isOwnProfile = currentUser && currentUser.username === username;

  useEffect(() => {
    fetchUserProfile();
  }, [username]);

  useEffect(() => {
    if (user) {
      fetchTweets(true);
    }
  }, [user, activeTab]);

  useEffect(() => {
    if (socket && user) {
      // Listen for follow/unfollow events
      socket.on('user_followed', (data) => {
        if (data.followedUserId === user._id) {
          setUser(prev => ({
            ...prev,
            followersCount: prev.followersCount + 1
          }));
        }
        if (data.followerId === currentUser?._id && data.followedUserId === user._id) {
          setFollowing(true);
        }
      });

      socket.on('user_unfollowed', (data) => {
        if (data.unfollowedUserId === user._id) {
          setUser(prev => ({
            ...prev,
            followersCount: Math.max(0, prev.followersCount - 1)
          }));
        }
        if (data.unfollowerId === currentUser?._id && data.unfollowedUserId === user._id) {
          setFollowing(false);
        }
      });

      return () => {
        socket.off('user_followed');
        socket.off('user_unfollowed');
      };
    }
  }, [socket, user, currentUser]);

  const fetchUserProfile = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/users/profile/${username}`);
      setUser(response.data.user);
      setFollowing(response.data.isFollowing);
    } catch (error) {
      console.error('Error fetching user profile:', error);
      if (error.response?.status === 404) {
        toast.error('用户不存在');
        navigate('/');
      } else {
        toast.error('获取用户信息失败');
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchTweets = async (reset = false) => {
    if (!user || tweetsLoading) return;

    try {
      setTweetsLoading(true);
      const currentPage = reset ? 1 : page;
      const params = {
        page: currentPage,
        limit: 10,
        type: activeTab
      };

      const response = await axios.get(`/api/users/${user._id}/tweets`, { params });
      const newTweets = response.data.tweets;

      if (reset) {
        setTweets(newTweets);
        setPage(2);
      } else {
        setTweets(prev => [...prev, ...newTweets]);
        setPage(prev => prev + 1);
      }

      setHasMore(newTweets.length === 10);
    } catch (error) {
      console.error('Error fetching tweets:', error);
      toast.error('获取推文失败');
    } finally {
      setTweetsLoading(false);
    }
  };

  const handleFollow = async () => {
    if (!currentUser) {
      toast.error('请先登录');
      return;
    }

    try {
      setFollowLoading(true);
      if (following) {
        await axios.delete(`/api/users/${user._id}/follow`);
        setFollowing(false);
        setUser(prev => ({
          ...prev,
          followersCount: Math.max(0, prev.followersCount - 1)
        }));
        toast.success('已取消关注');
      } else {
        await axios.post(`/api/users/${user._id}/follow`);
        setFollowing(true);
        setUser(prev => ({
          ...prev,
          followersCount: prev.followersCount + 1
        }));
        toast.success('关注成功');
      }
    } catch (error) {
      console.error('Error following/unfollowing user:', error);
      toast.error(following ? '取消关注失败' : '关注失败');
    } finally {
      setFollowLoading(false);
    }
  };

  const handleTweetUpdate = (updatedTweet) => {
    setTweets(prev => prev.map(tweet => 
      tweet._id === updatedTweet._id ? updatedTweet : tweet
    ));
  };

  const handleTweetDelete = (tweetId) => {
    setTweets(prev => prev.filter(tweet => tweet._id !== tweetId));
  };

  const handleNewTweet = (newTweet) => {
    if (activeTab === 'tweets' && newTweet.author._id === user._id) {
      setTweets(prev => [newTweet, ...prev]);
    }
    setShowComposer(false);
  };

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const tabs = [
    { id: 'tweets', label: '推文', count: user?.tweetsCount },
    { id: 'replies', label: '回复', count: user?.repliesCount },
    { id: 'media', label: '媒体', count: user?.mediaCount },
    { id: 'likes', label: '喜欢', count: user?.likesCount }
  ];

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">用户不存在</h2>
          <p className="text-gray-600 dark:text-gray-400">您访问的用户不存在或已被删除</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white dark:bg-dark-100">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white/80 dark:bg-dark-100/80 backdrop-blur-md border-b border-gray-200 dark:border-dark-300">
        <div className="max-w-2xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                {user.displayName}
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {formatNumber(user.tweetsCount)} 条推文
              </p>
            </div>
            {isOwnProfile && (
              <div className="relative">
                <button
                  onClick={() => setShowDropdown(!showDropdown)}
                  className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-dark-200 transition-colors"
                >
                  <EllipsisHorizontalIcon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                </button>
                {showDropdown && (
                  <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-dark-200 rounded-lg shadow-lg border border-gray-200 dark:border-dark-300 py-1 z-20">
                    <button
                      onClick={() => {
                        navigate('/settings/profile');
                        setShowDropdown(false);
                      }}
                      className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-300 flex items-center space-x-2"
                    >
                      <Cog6ToothIcon className="h-4 w-4" />
                      <span>编辑个人资料</span>
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="max-w-2xl mx-auto">
        {/* Profile Header */}
        <div className="relative">
          {/* Cover Image */}
          <div className="h-48 bg-gradient-to-r from-blue-400 to-purple-500 relative">
            {user.coverImage && (
              <img
                src={user.coverImage}
                alt="Cover"
                className="w-full h-full object-cover"
              />
            )}
          </div>

          {/* Profile Info */}
          <div className="px-4 pb-4">
            {/* Avatar */}
            <div className="relative -mt-16 mb-4">
              <div className="w-32 h-32 rounded-full border-4 border-white dark:border-dark-100 bg-white dark:bg-dark-200 overflow-hidden">
                {user.avatar ? (
                  <img
                    src={user.avatar}
                    alt={user.displayName}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gray-300 dark:bg-dark-300 flex items-center justify-center">
                    <span className="text-4xl font-bold text-gray-600 dark:text-gray-400">
                      {user.displayName.charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end mb-4">
              {isOwnProfile ? (
                <div className="flex space-x-2">
                  <button
                    onClick={() => setShowComposer(true)}
                    className="px-4 py-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors flex items-center space-x-2"
                  >
                    <PhotoIcon className="h-4 w-4" />
                    <span>发推文</span>
                  </button>
                  <button
                    onClick={() => navigate('/settings/profile')}
                    className="px-4 py-2 border border-gray-300 dark:border-dark-300 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors flex items-center space-x-2"
                  >
                    <Cog6ToothIcon className="h-4 w-4" />
                    <span>编辑资料</span>
                  </button>
                </div>
              ) : (
                <button
                  onClick={handleFollow}
                  disabled={followLoading}
                  className={`px-6 py-2 rounded-full font-medium transition-colors flex items-center space-x-2 ${
                    following
                      ? 'bg-red-500 hover:bg-red-600 text-white'
                      : 'bg-blue-500 hover:bg-blue-600 text-white'
                  } disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  {followLoading ? (
                    <LoadingSpinner size="sm" color="white" />
                  ) : following ? (
                    <>
                      <UserMinusIcon className="h-4 w-4" />
                      <span>取消关注</span>
                    </>
                  ) : (
                    <>
                      <UserPlusIcon className="h-4 w-4" />
                      <span>关注</span>
                    </>
                  )}
                </button>
              )}
            </div>

            {/* User Info */}
            <div className="space-y-3">
              <div>
                <div className="flex items-center space-x-2">
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                    {user.displayName}
                  </h2>
                  {user.verified && (
                    <CheckBadgeIcon className="h-5 w-5 text-blue-500" />
                  )}
                </div>
                <p className="text-gray-500 dark:text-gray-400">@{user.username}</p>
              </div>

              {user.bio && (
                <p className="text-gray-900 dark:text-white">{user.bio}</p>
              )}

              {/* Meta Info */}
              <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                {user.location && (
                  <div className="flex items-center space-x-1">
                    <MapPinIcon className="h-4 w-4" />
                    <span>{user.location}</span>
                  </div>
                )}
                {user.website && (
                  <div className="flex items-center space-x-1">
                    <LinkIcon className="h-4 w-4" />
                    <a
                      href={user.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-500 hover:underline"
                    >
                      {user.website.replace(/^https?:\/\//, '')}
                    </a>
                  </div>
                )}
                <div className="flex items-center space-x-1">
                  <CalendarIcon className="h-4 w-4" />
                  <span>加入于 {moment(user.createdAt).format('YYYY年M月')}</span>
                </div>
              </div>

              {/* Follow Stats */}
              <div className="flex space-x-6 text-sm">
                <button
                  onClick={() => navigate(`/profile/${user.username}/following`)}
                  className="hover:underline"
                >
                  <span className="font-bold text-gray-900 dark:text-white">
                    {formatNumber(user.followingCount)}
                  </span>
                  <span className="text-gray-500 dark:text-gray-400 ml-1">关注</span>
                </button>
                <button
                  onClick={() => navigate(`/profile/${user.username}/followers`)}
                  className="hover:underline"
                >
                  <span className="font-bold text-gray-900 dark:text-white">
                    {formatNumber(user.followersCount)}
                  </span>
                  <span className="text-gray-500 dark:text-gray-400 ml-1">粉丝</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-dark-300">
          <div className="flex">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => {
                  setActiveTab(tab.id);
                  setTweets([]);
                  setPage(1);
                  setHasMore(true);
                }}
                className={`flex-1 py-4 px-4 text-center font-medium transition-colors relative ${
                  activeTab === tab.id
                    ? 'text-blue-500 border-b-2 border-blue-500'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`}
              >
                <span>{tab.label}</span>
                {tab.count !== undefined && (
                  <span className="ml-1 text-xs">
                    ({formatNumber(tab.count)})
                  </span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Tweets */}
        <div className="pb-20">
          {tweets.length === 0 && !tweetsLoading ? (
            <div className="text-center py-12">
              <div className="text-gray-500 dark:text-gray-400">
                {activeTab === 'tweets' && isOwnProfile && (
                  <div>
                    <h3 className="text-lg font-medium mb-2">还没有推文</h3>
                    <p className="mb-4">分享您的第一条推文吧！</p>
                    <button
                      onClick={() => setShowComposer(true)}
                      className="px-6 py-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors"
                    >
                      发推文
                    </button>
                  </div>
                )}
                {activeTab === 'tweets' && !isOwnProfile && (
                  <div>
                    <h3 className="text-lg font-medium mb-2">还没有推文</h3>
                    <p>@{user.username} 还没有发布任何推文</p>
                  </div>
                )}
                {activeTab === 'replies' && (
                  <div>
                    <h3 className="text-lg font-medium mb-2">还没有回复</h3>
                    <p>@{user.username} 还没有回复任何推文</p>
                  </div>
                )}
                {activeTab === 'media' && (
                  <div>
                    <h3 className="text-lg font-medium mb-2">还没有媒体</h3>
                    <p>@{user.username} 还没有发布包含媒体的推文</p>
                  </div>
                )}
                {activeTab === 'likes' && (
                  <div>
                    <h3 className="text-lg font-medium mb-2">还没有喜欢</h3>
                    <p>@{user.username} 还没有喜欢任何推文</p>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <InfiniteScroll
              dataLength={tweets.length}
              next={() => fetchTweets(false)}
              hasMore={hasMore}
              loader={
                <div className="flex justify-center py-4">
                  <LoadingSpinner size="sm" />
                </div>
              }
              endMessage={
                tweets.length > 0 && (
                  <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                    没有更多推文了
                  </div>
                )
              }
            >
              {tweets.map((tweet) => (
                <TweetCard
                  key={tweet._id}
                  tweet={tweet}
                  onUpdate={handleTweetUpdate}
                  onDelete={handleTweetDelete}
                />
              ))}
            </InfiniteScroll>
          )}
        </div>
      </div>

      {/* Tweet Composer Modal */}
      {showComposer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-dark-200 rounded-lg w-full max-w-lg max-h-[80vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-dark-300">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">发推文</h3>
              <button
                onClick={() => setShowComposer(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-4">
              <TweetComposer onTweetCreated={handleNewTweet} />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Profile;