import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';
import toast from 'react-hot-toast';
import {
  ArrowLeftIcon,
  LockClosedIcon,
  GlobeAltIcon,
  PhotoIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../components/UI/LoadingSpinner';

function EditList() {
  const { listId } = useParams();
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [list, setList] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isPrivate: false
  });
  
  const [bannerFile, setBannerFile] = useState(null);
  const [bannerPreview, setBannerPreview] = useState(null);
  const [uploadingBanner, setUploadingBanner] = useState(false);

  useEffect(() => {
    if (listId) {
      fetchList();
    }
  }, [listId]);

  const fetchList = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/lists/${listId}`);
      const listData = response.data.list;
      
      // Check if user is the owner
      if (!user || listData.owner._id !== user._id) {
        toast.error('您没有权限编辑此列表');
        navigate(`/lists/${listId}`);
        return;
      }
      
      setList(listData);
      setFormData({
        name: listData.name,
        description: listData.description || '',
        isPrivate: listData.isPrivate
      });
      
      if (listData.banner) {
        setBannerPreview(listData.banner);
      }
    } catch (error) {
      console.error('Error fetching list:', error);
      if (error.response?.status === 404) {
        toast.error('列表不存在');
        navigate('/lists');
      } else if (error.response?.status === 403) {
        toast.error('您没有权限编辑此列表');
        navigate('/lists');
      } else {
        toast.error('获取列表失败');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleBannerChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('请选择图片文件');
      return;
    }
    
    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('图片大小不能超过5MB');
      return;
    }
    
    setBannerFile(file);
    
    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setBannerPreview(e.target.result);
    };
    reader.readAsDataURL(file);
  };

  const handleRemoveBanner = () => {
    setBannerFile(null);
    setBannerPreview(null);
  };

  const uploadBanner = async () => {
    if (!bannerFile) return null;
    
    try {
      setUploadingBanner(true);
      const formData = new FormData();
      formData.append('banner', bannerFile);
      
      const response = await axios.post(`/api/lists/${listId}/banner`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      return response.data.bannerUrl;
    } catch (error) {
      console.error('Error uploading banner:', error);
      toast.error('上传横幅失败');
      throw error;
    } finally {
      setUploadingBanner(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('请输入列表名称');
      return;
    }
    
    try {
      setSaving(true);
      
      let bannerUrl = list.banner;
      
      // Upload banner if changed
      if (bannerFile) {
        bannerUrl = await uploadBanner();
      } else if (bannerPreview === null && list.banner) {
        // Remove banner if it was removed
        await axios.delete(`/api/lists/${listId}/banner`);
        bannerUrl = null;
      }
      
      // Update list
      const updateData = {
        ...formData,
        banner: bannerUrl
      };
      
      const response = await axios.put(`/api/lists/${listId}`, updateData);
      
      toast.success('列表更新成功');
      navigate(`/lists/${listId}`);
    } catch (error) {
      console.error('Error updating list:', error);
      toast.error('更新列表失败');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!list) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            列表不存在
          </h2>
          <p className="text-gray-500 dark:text-gray-400">
            您要编辑的列表可能已被删除或您没有权限访问。
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white dark:bg-dark-100">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white/80 dark:bg-dark-100/80 backdrop-blur-md border-b border-gray-200 dark:border-dark-300">
        <div className="max-w-2xl mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => navigate(`/lists/${listId}`)}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-dark-300 rounded-full transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5" />
            </button>
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">
              编辑列表
            </h1>
          </div>
          
          <button
            type="submit"
            form="edit-list-form"
            disabled={saving || !formData.name.trim()}
            className="px-4 py-1.5 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {saving ? <LoadingSpinner size="sm" color="white" /> : '保存'}
          </button>
        </div>
      </div>

      {/* Form */}
      <div className="max-w-2xl mx-auto p-4">
        <form id="edit-list-form" onSubmit={handleSubmit} className="space-y-6">
          {/* Banner */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              横幅图片
            </label>
            <div className="relative">
              {bannerPreview ? (
                <div className="relative w-full h-48 rounded-lg overflow-hidden bg-gray-100 dark:bg-dark-300">
                  <img
                    src={bannerPreview}
                    alt="Banner preview"
                    className="w-full h-full object-cover"
                  />
                  <button
                    type="button"
                    onClick={handleRemoveBanner}
                    className="absolute top-2 right-2 p-1.5 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-colors"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                  {uploadingBanner && (
                    <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                      <LoadingSpinner size="md" color="white" />
                    </div>
                  )}
                </div>
              ) : (
                <label className="flex flex-col items-center justify-center w-full h-48 border-2 border-gray-300 dark:border-dark-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:bg-dark-200 hover:bg-gray-100 dark:hover:bg-dark-300 transition-colors">
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <PhotoIcon className="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" />
                    <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                      <span className="font-semibold">点击上传</span> 或拖拽图片到此处
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      PNG, JPG 或 GIF (最大 5MB)
                    </p>
                  </div>
                  <input
                    type="file"
                    className="hidden"
                    accept="image/*"
                    onChange={handleBannerChange}
                    disabled={saving}
                  />
                </label>
              )}
            </div>
          </div>

          {/* Name */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              列表名称 *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-dark-300 rounded-lg bg-white dark:bg-dark-200 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="输入列表名称"
              maxLength={25}
              disabled={saving}
              required
            />
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              {formData.name.length}/25
            </p>
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              描述
            </label>
            <textarea
              id="description"
              name="description"
              rows={4}
              value={formData.description}
              onChange={handleInputChange}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-dark-300 rounded-lg bg-white dark:bg-dark-200 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              placeholder="描述这个列表的用途（可选）"
              maxLength={100}
              disabled={saving}
            />
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              {formData.description.length}/100
            </p>
          </div>

          {/* Privacy */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              隐私设置
            </label>
            <div className="space-y-3">
              <label className="flex items-start space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="isPrivate"
                  checked={!formData.isPrivate}
                  onChange={() => setFormData(prev => ({ ...prev, isPrivate: false }))}
                  className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-dark-300"
                  disabled={saving}
                />
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <GlobeAltIcon className="h-5 w-5 text-gray-400" />
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      公开列表
                    </span>
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    任何人都可以查看此列表和其中的推文
                  </p>
                </div>
              </label>
              
              <label className="flex items-start space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="isPrivate"
                  checked={formData.isPrivate}
                  onChange={() => setFormData(prev => ({ ...prev, isPrivate: true }))}
                  className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-dark-300"
                  disabled={saving}
                />
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <LockClosedIcon className="h-5 w-5 text-gray-400" />
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      私密列表
                    </span>
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    只有您可以查看此列表和其中的推文
                  </p>
                </div>
              </label>
            </div>
          </div>

          {/* Additional Info */}
          <div className="bg-gray-50 dark:bg-dark-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
              关于列表编辑
            </h3>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>• 更改隐私设置会立即生效</li>
              <li>• 列表成员不会因为隐私设置更改而被移除</li>
              <li>• 横幅图片建议尺寸为 1500x500 像素</li>
              <li>• 列表名称和描述的更改会通知所有关注者</li>
            </ul>
          </div>
        </form>
      </div>
    </div>
  );
}

export default EditList;