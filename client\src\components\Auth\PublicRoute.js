import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../UI/LoadingSpinner';

function PublicRoute() {
  const { user, loading, isAuthenticated } = useAuth();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-dark-100 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">
            正在加载...
          </p>
        </div>
      </div>
    );
  }

  // Redirect to home if already authenticated
  if (isAuthenticated && user) {
    return <Navigate to="/" replace />;
  }

  // Render public content
  return <Outlet />;
}

export default PublicRoute;