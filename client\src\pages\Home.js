import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import TweetComposer from '../components/Tweet/TweetComposer';
import TweetCard from '../components/Tweet/TweetCard';
import LoadingSpinner, { LoadingCard } from '../components/UI/LoadingSpinner';
import InfiniteScroll from 'react-infinite-scroll-component';
import axios from 'axios';
import toast from 'react-hot-toast';

function Home() {
  const { user } = useAuth();
  const [tweets, setTweets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch tweets
  const fetchTweets = useCallback(async (pageNum = 1, isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else if (pageNum === 1) {
        setLoading(true);
      }

      const response = await axios.get('/tweets/timeline', {
        params: {
          page: pageNum,
          limit: 10
        }
      });

      const newTweets = response.data.tweets || [];
      
      if (pageNum === 1 || isRefresh) {
        setTweets(newTweets);
      } else {
        setTweets(prev => [...prev, ...newTweets]);
      }

      setHasMore(newTweets.length === 10);
      setPage(pageNum);
    } catch (error) {
      console.error('Failed to fetch tweets:', error);
      toast.error('获取推文失败，请重试');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  // Initial load
  useEffect(() => {
    fetchTweets(1);
  }, [fetchTweets]);

  // Listen for new tweets
  useEffect(() => {
    const handleNewTweet = (event) => {
      const newTweet = event.detail;
      // Only add to timeline if it's from someone the user follows or the user themselves
      if (newTweet.author._id === user._id || newTweet.isFromFollowing) {
        setTweets(prev => [newTweet, ...prev]);
      }
    };

    window.addEventListener('newTweet', handleNewTweet);
    return () => window.removeEventListener('newTweet', handleNewTweet);
  }, [user._id]);

  // Load more tweets
  const loadMore = () => {
    if (!loading && hasMore) {
      fetchTweets(page + 1);
    }
  };

  // Refresh tweets
  const handleRefresh = () => {
    fetchTweets(1, true);
  };

  // Handle tweet success
  const handleTweetSuccess = (newTweet) => {
    setTweets(prev => [newTweet, ...prev]);
  };

  // Update tweet in list
  const updateTweet = (tweetId, updates) => {
    setTweets(prev => prev.map(tweet => 
      tweet._id === tweetId ? { ...tweet, ...updates } : tweet
    ));
  };

  // Remove tweet from list
  const removeTweet = (tweetId) => {
    setTweets(prev => prev.filter(tweet => tweet._id !== tweetId));
  };

  if (loading && tweets.length === 0) {
    return (
      <div className="min-h-screen">
        {/* Header */}
        <div className="sticky top-0 z-10 bg-white dark:bg-dark-200 border-b border-gray-200 dark:border-dark-300 backdrop-blur-md bg-opacity-80 dark:bg-opacity-80">
          <div className="flex items-center justify-between p-4">
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">
              首页
            </h1>
            <button
              onClick={handleRefresh}
              className="p-2 hover:bg-gray-100 dark:hover:bg-dark-300 rounded-full transition-colors"
              disabled={refreshing}
            >
              <svg className={`w-5 h-5 text-gray-600 dark:text-gray-400 ${refreshing ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>
        </div>

        {/* Tweet Composer */}
        <TweetComposer onSuccess={handleTweetSuccess} />

        {/* Loading Skeletons */}
        <div className="space-y-0">
          {Array.from({ length: 5 }).map((_, index) => (
            <LoadingCard key={index} className="border-b border-gray-200 dark:border-dark-300" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white dark:bg-dark-200 border-b border-gray-200 dark:border-dark-300 backdrop-blur-md bg-opacity-80 dark:bg-opacity-80">
        <div className="flex items-center justify-between p-4">
          <h1 className="text-xl font-bold text-gray-900 dark:text-white">
            首页
          </h1>
          <button
            onClick={handleRefresh}
            className="p-2 hover:bg-gray-100 dark:hover:bg-dark-300 rounded-full transition-colors"
            disabled={refreshing}
          >
            <svg className={`w-5 h-5 text-gray-600 dark:text-gray-400 ${refreshing ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
      </div>

      {/* Tweet Composer */}
      <TweetComposer onSuccess={handleTweetSuccess} />

      {/* Timeline */}
      {tweets.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 dark:text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            <h3 className="text-xl font-semibold mb-2">欢迎来到 Twitter！</h3>
            <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
              这里还没有推文。关注一些用户或发布你的第一条推文来开始吧！
            </p>
          </div>
        </div>
      ) : (
        <InfiniteScroll
          dataLength={tweets.length}
          next={loadMore}
          hasMore={hasMore}
          loader={
            <div className="flex justify-center py-4">
              <LoadingSpinner />
            </div>
          }
          endMessage={
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <p>你已经看完所有推文了</p>
            </div>
          }
          refreshFunction={handleRefresh}
          pullDownToRefresh={false}
        >
          <div className="space-y-0">
            {tweets.map((tweet) => (
              <TweetCard
                key={tweet._id}
                tweet={tweet}
                onUpdate={updateTweet}
                onDelete={removeTweet}
              />
            ))}
          </div>
        </InfiniteScroll>
      )}
    </div>
  );
}

export default Home;