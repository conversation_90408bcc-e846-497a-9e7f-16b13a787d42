import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from './contexts/AuthContext';
import { useTheme } from './contexts/ThemeContext';

// Layout Components
import Layout from './components/Layout/Layout';
import AuthLayout from './components/Layout/AuthLayout';

// Pages
import Home from './pages/Home';
import Explore from './pages/Explore';
import Notifications from './pages/Notifications';
import Messages from './pages/Messages';
import Profile from './pages/Profile';
import TweetDetail from './pages/TweetDetail';
import Search from './pages/Search';
import Settings from './pages/Settings';

// Auth Pages
import Login from './pages/Auth/Login';
import Register from './pages/Auth/Register';
import ForgotPassword from './pages/Auth/ForgotPassword';

// Components
import LoadingSpinner from './components/UI/LoadingSpinner';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import PublicRoute from './components/Auth/PublicRoute';

function App() {
  const { user, loading } = useAuth();
  const { darkMode } = useTheme();

  // Apply dark mode class to html element
  React.useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [darkMode]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-dark-100 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-dark-100 transition-colors duration-200">
      <Routes>
        {/* Public Routes */}
        <Route path="/auth" element={<PublicRoute />}>
          <Route path="" element={<AuthLayout />}>
            <Route path="login" element={<Login />} />
            <Route path="register" element={<Register />} />
            <Route path="forgot-password" element={<ForgotPassword />} />
            <Route path="" element={<Navigate to="login" replace />} />
          </Route>
        </Route>

        {/* Protected Routes */}
        <Route path="/" element={<ProtectedRoute />}>
          <Route path="" element={<Layout />}>
            {/* Main Routes */}
            <Route index element={<Home />} />
            <Route path="home" element={<Navigate to="/" replace />} />
            <Route path="explore" element={<Explore />} />
            <Route path="notifications" element={<Notifications />} />
            <Route path="messages" element={<Messages />} />
            <Route path="settings" element={<Settings />} />
            
            {/* Search */}
            <Route path="search" element={<Search />} />
            
            {/* Tweet Detail */}
            <Route path="tweet/:id" element={<TweetDetail />} />
            
            {/* User Profile Routes */}
            <Route path=":username" element={<Profile />} />
            <Route path=":username/with_replies" element={<Profile tab="replies" />} />
            <Route path=":username/media" element={<Profile tab="media" />} />
            <Route path=":username/likes" element={<Profile tab="likes" />} />
            <Route path=":username/followers" element={<Profile tab="followers" />} />
            <Route path=":username/following" element={<Profile tab="following" />} />
            
            {/* Hashtag */}
            <Route path="hashtag/:hashtag" element={<Search type="hashtag" />} />
            
            {/* Catch all route */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Route>
        </Route>

        {/* Redirect to auth if not authenticated */}
        <Route path="*" element={
          user ? <Navigate to="/" replace /> : <Navigate to="/auth/login" replace />
        } />
      </Routes>
    </div>
  );
}

export default App;