import React from 'react';

function LoadingSpinner({ size = 'md', className = '', color = 'twitter-blue' }) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  const colorClasses = {
    'twitter-blue': 'border-twitter-blue',
    'gray': 'border-gray-400',
    'white': 'border-white',
    'red': 'border-red-500',
    'green': 'border-green-500'
  };

  return (
    <div className={`inline-block ${className}`}>
      <div
        className={`animate-spin rounded-full border-2 border-t-transparent ${sizeClasses[size]} ${colorClasses[color]}`}
        role="status"
        aria-label="加载中"
      >
        <span className="sr-only">加载中...</span>
      </div>
    </div>
  );
}

// Loading skeleton for content
export function LoadingSkeleton({ className = '', lines = 3 }) {
  return (
    <div className={`animate-pulse ${className}`}>
      {Array.from({ length: lines }).map((_, index) => (
        <div
          key={index}
          className={`bg-gray-200 dark:bg-dark-300 rounded h-4 mb-2 ${
            index === lines - 1 ? 'w-3/4' : 'w-full'
          }`}
        />
      ))}
    </div>
  );
}

// Loading card skeleton
export function LoadingCard({ className = '' }) {
  return (
    <div className={`animate-pulse p-4 ${className}`}>
      <div className="flex space-x-3">
        <div className="w-10 h-10 bg-gray-200 dark:bg-dark-300 rounded-full" />
        <div className="flex-1 space-y-2">
          <div className="h-4 bg-gray-200 dark:bg-dark-300 rounded w-1/4" />
          <div className="h-4 bg-gray-200 dark:bg-dark-300 rounded w-full" />
          <div className="h-4 bg-gray-200 dark:bg-dark-300 rounded w-3/4" />
        </div>
      </div>
    </div>
  );
}

// Loading dots animation
export function LoadingDots({ className = '' }) {
  return (
    <div className={`flex space-x-1 ${className}`}>
      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
    </div>
  );
}

// Full page loading
export function FullPageLoading({ message = '加载中...' }) {
  return (
    <div className="fixed inset-0 bg-white dark:bg-dark-100 flex items-center justify-center z-50">
      <div className="text-center">
        <LoadingSpinner size="xl" />
        <p className="mt-4 text-gray-600 dark:text-gray-400 text-lg">
          {message}
        </p>
      </div>
    </div>
  );
}

// Button loading state
export function ButtonLoading({ children, loading, disabled, className = '', ...props }) {
  return (
    <button
      disabled={disabled || loading}
      className={`relative ${className} ${loading ? 'cursor-not-allowed' : ''}`}
      {...props}
    >
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <LoadingSpinner size="sm" color="white" />
        </div>
      )}
      <span className={loading ? 'opacity-0' : 'opacity-100'}>
        {children}
      </span>
    </button>
  );
}

export default LoadingSpinner;