@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

.dark ::-webkit-scrollbar-thumb {
  background: #4a5568;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #718096;
}

/* Custom components */
@layer components {
  .btn-primary {
    @apply bg-twitter-blue hover:bg-twitter-darkBlue text-white font-medium py-2 px-4 rounded-full transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 dark:bg-dark-300 dark:hover:bg-dark-400 text-gray-900 dark:text-white font-medium py-2 px-4 rounded-full transition-colors duration-200;
  }
  
  .btn-outline {
    @apply border border-gray-300 dark:border-dark-400 hover:bg-gray-50 dark:hover:bg-dark-300 text-gray-900 dark:text-white font-medium py-2 px-4 rounded-full transition-colors duration-200;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 dark:border-dark-400 rounded-lg focus:outline-none focus:ring-2 focus:ring-twitter-blue focus:border-transparent bg-white dark:bg-dark-200 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400;
  }
  
  .textarea-field {
    @apply w-full px-3 py-2 border border-gray-300 dark:border-dark-400 rounded-lg focus:outline-none focus:ring-2 focus:ring-twitter-blue focus:border-transparent bg-white dark:bg-dark-200 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none;
  }
  
  .card {
    @apply bg-white dark:bg-dark-200 border border-gray-200 dark:border-dark-300 rounded-lg shadow-sm;
  }
  
  .sidebar-item {
    @apply flex items-center space-x-3 px-4 py-3 rounded-full hover:bg-gray-100 dark:hover:bg-dark-300 transition-colors duration-200 cursor-pointer;
  }
  
  .sidebar-item.active {
    @apply bg-gray-100 dark:bg-dark-300 font-semibold;
  }
  
  .tweet-card {
    @apply bg-white dark:bg-dark-200 border-b border-gray-200 dark:border-dark-300 hover:bg-gray-50 dark:hover:bg-dark-300 transition-colors duration-200 cursor-pointer;
  }
  
  .tweet-action {
    @apply flex items-center space-x-2 text-gray-500 dark:text-gray-400 hover:text-twitter-blue transition-colors duration-200 cursor-pointer;
  }
  
  .tweet-action.liked {
    @apply text-red-500 hover:text-red-600;
  }
  
  .tweet-action.retweeted {
    @apply text-green-500 hover:text-green-600;
  }
  
  .notification-item {
    @apply bg-white dark:bg-dark-200 border-b border-gray-200 dark:border-dark-300 hover:bg-gray-50 dark:hover:bg-dark-300 transition-colors duration-200;
  }
  
  .notification-item.unread {
    @apply bg-blue-50 dark:bg-blue-900/20 border-l-4 border-l-twitter-blue;
  }
  
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4;
  }
  
  .modal-content {
    @apply bg-white dark:bg-dark-200 rounded-lg shadow-xl max-w-lg w-full max-h-[90vh] overflow-y-auto;
  }
  
  .dropdown-menu {
    @apply absolute right-0 mt-2 w-48 bg-white dark:bg-dark-200 border border-gray-200 dark:border-dark-300 rounded-lg shadow-lg z-10 py-1;
  }
  
  .dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-300 cursor-pointer;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full h-6 w-6 border-b-2 border-twitter-blue;
  }
  
  .hashtag {
    @apply text-twitter-blue hover:underline cursor-pointer;
  }
  
  .mention {
    @apply text-twitter-blue hover:underline cursor-pointer;
  }
  
  .link {
    @apply text-twitter-blue hover:underline;
  }
}

/* Custom utilities */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .gradient-text {
    background: linear-gradient(45deg, #1DA1F2, #1A91DA);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
  }
  
  .dark .glass-effect {
    background: rgba(0, 0, 0, 0.2);
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.bounce-in {
  animation: bounceIn 0.6s ease-out;
}

/* Focus styles */
.focus-visible:focus {
  outline: 2px solid #1DA1F2;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .btn-primary {
    @apply border-2 border-black;
  }
  
  .card {
    @apply border-2 border-gray-800;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Mobile optimizations */
@media (max-width: 640px) {
  .modal-content {
    @apply mx-2 max-h-[95vh];
  }
  
  .sidebar-item {
    @apply px-2 py-2;
  }
}

/* Tablet optimizations */
@media (min-width: 641px) and (max-width: 1024px) {
  .sidebar-item {
    @apply px-3 py-2.5;
  }
}