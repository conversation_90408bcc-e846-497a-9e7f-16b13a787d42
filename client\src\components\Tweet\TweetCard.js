import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useSocket } from '../../contexts/SocketContext';
import moment from 'moment';
import {
  ChatBubbleOvalLeftIcon,
  ArrowPathIcon,
  HeartIcon,
  ShareIcon,
  EllipsisHorizontalIcon,
  TrashIcon,
  PencilIcon,
  FlagIcon,
  UserPlusIcon,
  UserMinusIcon
} from '@heroicons/react/24/outline';
import {
  HeartIcon as HeartIconSolid,
  ArrowPathIcon as ArrowPathIconSolid
} from '@heroicons/react/24/solid';
import axios from 'axios';
import toast from 'react-hot-toast';
import TweetComposer from './TweetComposer';

function TweetCard({ tweet, onUpdate, onDelete, showThread = false }) {
  const { user } = useAuth();
  const { emit } = useSocket();
  const navigate = useNavigate();
  const [showDropdown, setShowDropdown] = useState(false);
  const [showReplyComposer, setShowReplyComposer] = useState(false);
  const [showQuoteComposer, setShowQuoteComposer] = useState(false);
  const [isLiked, setIsLiked] = useState(tweet.isLiked || false);
  const [isRetweeted, setIsRetweeted] = useState(tweet.isRetweeted || false);
  const [likesCount, setLikesCount] = useState(tweet.likesCount || 0);
  const [retweetsCount, setRetweetsCount] = useState(tweet.retweetsCount || 0);
  const [repliesCount, setRepliesCount] = useState(tweet.repliesCount || 0);
  const [isFollowing, setIsFollowing] = useState(tweet.author.isFollowing || false);
  const dropdownRef = useRef(null);

  const isOwner = tweet.author._id === user?._id;
  const isRetweet = tweet.isRetweet && tweet.originalTweet;
  const displayTweet = isRetweet ? tweet.originalTweet : tweet;

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Listen for real-time updates
  useEffect(() => {
    const handleTweetLiked = (event) => {
      const { tweetId, isLiked: liked, likesCount: count } = event.detail;
      if (tweetId === displayTweet._id) {
        setIsLiked(liked);
        setLikesCount(count);
      }
    };

    const handleTweetRetweeted = (event) => {
      const { tweetId, isRetweeted: retweeted, retweetsCount: count } = event.detail;
      if (tweetId === displayTweet._id) {
        setIsRetweeted(retweeted);
        setRetweetsCount(count);
      }
    };

    window.addEventListener('tweetLiked', handleTweetLiked);
    window.addEventListener('tweetRetweeted', handleTweetRetweeted);

    return () => {
      window.removeEventListener('tweetLiked', handleTweetLiked);
      window.removeEventListener('tweetRetweeted', handleTweetRetweeted);
    };
  }, [displayTweet._id]);

  const handleLike = async (e) => {
    e.stopPropagation();
    
    try {
      const response = await axios.post(`/tweets/${displayTweet._id}/like`);
      const newIsLiked = response.data.isLiked;
      const newCount = response.data.likesCount;
      
      setIsLiked(newIsLiked);
      setLikesCount(newCount);
      
      // Emit real-time event
      emit('tweet_liked', {
        tweetId: displayTweet._id,
        isLiked: newIsLiked,
        likesCount: newCount,
        userId: user._id
      });
      
      if (onUpdate) {
        onUpdate(displayTweet._id, { isLiked: newIsLiked, likesCount: newCount });
      }
    } catch (error) {
      toast.error('操作失败，请重试');
    }
  };

  const handleRetweet = async (e) => {
    e.stopPropagation();
    
    try {
      const response = await axios.post(`/tweets/${displayTweet._id}/retweet`);
      const newIsRetweeted = response.data.isRetweeted;
      const newCount = response.data.retweetsCount;
      
      setIsRetweeted(newIsRetweeted);
      setRetweetsCount(newCount);
      
      // Emit real-time event
      emit('tweet_retweeted', {
        tweetId: displayTweet._id,
        isRetweeted: newIsRetweeted,
        retweetsCount: newCount,
        userId: user._id
      });
      
      if (onUpdate) {
        onUpdate(displayTweet._id, { isRetweeted: newIsRetweeted, retweetsCount: newCount });
      }
      
      toast.success(newIsRetweeted ? '转发成功' : '取消转发');
    } catch (error) {
      toast.error('操作失败，请重试');
    }
  };

  const handleReply = (e) => {
    e.stopPropagation();
    setShowReplyComposer(true);
  };

  const handleQuote = (e) => {
    e.stopPropagation();
    setShowQuoteComposer(true);
  };

  const handleShare = async (e) => {
    e.stopPropagation();
    
    const url = `${window.location.origin}/tweet/${displayTweet._id}`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${displayTweet.author.displayName || displayTweet.author.username} 的推文`,
          text: displayTweet.content,
          url: url
        });
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      // Fallback to clipboard
      try {
        await navigator.clipboard.writeText(url);
        toast.success('链接已复制到剪贴板');
      } catch (error) {
        toast.error('分享失败');
      }
    }
  };

  const handleDelete = async () => {
    if (!window.confirm('确定要删除这条推文吗？')) {
      return;
    }
    
    try {
      await axios.delete(`/tweets/${displayTweet._id}`);
      toast.success('推文已删除');
      if (onDelete) {
        onDelete(displayTweet._id);
      }
    } catch (error) {
      toast.error('删除失败，请重试');
    }
    setShowDropdown(false);
  };

  const handleFollow = async (e) => {
    e.stopPropagation();
    
    try {
      await axios.post(`/users/${displayTweet.author._id}/follow`);
      setIsFollowing(!isFollowing);
      toast.success(isFollowing ? '已取消关注' : '关注成功');
    } catch (error) {
      toast.error('操作失败，请重试');
    }
    setShowDropdown(false);
  };

  const handleCardClick = () => {
    navigate(`/tweet/${displayTweet._id}`);
  };

  const formatContent = (content) => {
    return content
      .replace(/#(\w+)/g, '<span class="hashtag">#$1</span>')
      .replace(/@(\w+)/g, '<span class="mention">@$1</span>')
      .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" class="link" target="_blank" rel="noopener noreferrer">$1</a>');
  };

  const handleReplySuccess = (newReply) => {
    setRepliesCount(prev => prev + 1);
    setShowReplyComposer(false);
    if (onUpdate) {
      onUpdate(displayTweet._id, { repliesCount: repliesCount + 1 });
    }
  };

  const handleQuoteSuccess = () => {
    setShowQuoteComposer(false);
    toast.success('引用推文发布成功');
  };

  return (
    <>
      <article className="tweet-card p-4" onClick={handleCardClick}>
        {/* Retweet indicator */}
        {isRetweet && (
          <div className="flex items-center space-x-2 mb-2 text-gray-500 dark:text-gray-400 text-sm">
            <ArrowPathIconSolid className="w-4 h-4" />
            <span>
              <Link 
                to={`/${tweet.author.username}`} 
                className="hover:underline"
                onClick={(e) => e.stopPropagation()}
              >
                {tweet.author.displayName || tweet.author.username}
              </Link>
              {' '}转发了
            </span>
          </div>
        )}

        <div className="flex space-x-3">
          {/* Avatar */}
          <Link 
            to={`/${displayTweet.author.username}`}
            onClick={(e) => e.stopPropagation()}
            className="flex-shrink-0"
          >
            <img
              src={displayTweet.author.avatar || '/default-avatar.png'}
              alt={displayTweet.author.displayName || displayTweet.author.username}
              className="w-10 h-10 rounded-full hover:opacity-90 transition-opacity"
            />
          </Link>

          {/* Content */}
          <div className="flex-1 min-w-0">
            {/* Header */}
            <div className="flex items-center space-x-2 mb-1">
              <Link 
                to={`/${displayTweet.author.username}`}
                onClick={(e) => e.stopPropagation()}
                className="font-semibold text-gray-900 dark:text-white hover:underline truncate"
              >
                {displayTweet.author.displayName || displayTweet.author.username}
              </Link>
              {displayTweet.author.verified && (
                <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              )}
              <span className="text-gray-500 dark:text-gray-400 text-sm truncate">
                @{displayTweet.author.username}
              </span>
              <span className="text-gray-500 dark:text-gray-400 text-sm">·</span>
              <span className="text-gray-500 dark:text-gray-400 text-sm hover:underline" title={moment(displayTweet.createdAt).format('YYYY年MM月DD日 HH:mm')}>
                {moment(displayTweet.createdAt).fromNow()}
              </span>
              
              {/* More options */}
              <div className="ml-auto relative" ref={dropdownRef}>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowDropdown(!showDropdown);
                  }}
                  className="p-1 hover:bg-gray-100 dark:hover:bg-dark-300 rounded-full transition-colors"
                >
                  <EllipsisHorizontalIcon className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                </button>
                
                {showDropdown && (
                  <div className="dropdown-menu">
                    {isOwner ? (
                      <>
                        <button className="dropdown-item text-red-600 dark:text-red-400" onClick={handleDelete}>
                          <TrashIcon className="w-4 h-4 mr-2" />
                          删除推文
                        </button>
                      </>
                    ) : (
                      <>
                        <button className="dropdown-item" onClick={handleFollow}>
                          {isFollowing ? (
                            <>
                              <UserMinusIcon className="w-4 h-4 mr-2" />
                              取消关注 @{displayTweet.author.username}
                            </>
                          ) : (
                            <>
                              <UserPlusIcon className="w-4 h-4 mr-2" />
                              关注 @{displayTweet.author.username}
                            </>
                          )}
                        </button>
                        <button className="dropdown-item text-red-600 dark:text-red-400">
                          <FlagIcon className="w-4 h-4 mr-2" />
                          举报推文
                        </button>
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Tweet content */}
            <div className="text-gray-900 dark:text-white mb-3">
              <p dangerouslySetInnerHTML={{ __html: formatContent(displayTweet.content) }} />
            </div>

            {/* Images */}
            {displayTweet.images && displayTweet.images.length > 0 && (
              <div className={`mb-3 grid gap-2 rounded-lg overflow-hidden ${
                displayTweet.images.length === 1 ? 'grid-cols-1' :
                displayTweet.images.length === 2 ? 'grid-cols-2' :
                displayTweet.images.length === 3 ? 'grid-cols-2' :
                'grid-cols-2'
              }`}>
                {displayTweet.images.map((image, index) => (
                  <div 
                    key={index} 
                    className={displayTweet.images.length === 3 && index === 0 ? 'row-span-2' : ''}
                    onClick={(e) => e.stopPropagation()}
                  >
                    <img
                      src={image}
                      alt="Tweet image"
                      className="w-full h-full object-cover hover:opacity-95 transition-opacity cursor-pointer"
                      style={{ maxHeight: displayTweet.images.length === 1 ? '400px' : '200px' }}
                    />
                  </div>
                ))}
              </div>
            )}

            {/* Quote tweet */}
            {displayTweet.quoteTweet && (
              <div 
                className="border border-gray-200 dark:border-dark-300 rounded-lg p-3 mb-3 hover:bg-gray-50 dark:hover:bg-dark-300 transition-colors cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(`/tweet/${displayTweet.quoteTweet._id}`);
                }}
              >
                <div className="flex items-center space-x-2 mb-2">
                  <img
                    src={displayTweet.quoteTweet.author.avatar || '/default-avatar.png'}
                    alt={displayTweet.quoteTweet.author.displayName}
                    className="w-5 h-5 rounded-full"
                  />
                  <span className="font-medium text-sm text-gray-900 dark:text-white">
                    {displayTweet.quoteTweet.author.displayName || displayTweet.quoteTweet.author.username}
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    @{displayTweet.quoteTweet.author.username}
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">·</span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {moment(displayTweet.quoteTweet.createdAt).fromNow()}
                  </span>
                </div>
                <p className="text-sm text-gray-900 dark:text-white">
                  {displayTweet.quoteTweet.content}
                </p>
              </div>
            )}

            {/* Actions */}
            <div className="flex items-center justify-between max-w-md mt-3">
              <button
                onClick={handleReply}
                className="tweet-action group"
              >
                <div className="p-2 rounded-full group-hover:bg-blue-50 dark:group-hover:bg-blue-900/20 transition-colors">
                  <ChatBubbleOvalLeftIcon className="w-5 h-5" />
                </div>
                {repliesCount > 0 && <span className="text-sm">{repliesCount}</span>}
              </button>

              <button
                onClick={handleRetweet}
                className={`tweet-action group ${isRetweeted ? 'retweeted' : ''}`}
              >
                <div className="p-2 rounded-full group-hover:bg-green-50 dark:group-hover:bg-green-900/20 transition-colors">
                  {isRetweeted ? (
                    <ArrowPathIconSolid className="w-5 h-5" />
                  ) : (
                    <ArrowPathIcon className="w-5 h-5" />
                  )}
                </div>
                {retweetsCount > 0 && <span className="text-sm">{retweetsCount}</span>}
              </button>

              <button
                onClick={handleLike}
                className={`tweet-action group ${isLiked ? 'liked' : ''}`}
              >
                <div className="p-2 rounded-full group-hover:bg-red-50 dark:group-hover:bg-red-900/20 transition-colors">
                  {isLiked ? (
                    <HeartIconSolid className="w-5 h-5" />
                  ) : (
                    <HeartIcon className="w-5 h-5" />
                  )}
                </div>
                {likesCount > 0 && <span className="text-sm">{likesCount}</span>}
              </button>

              <button
                onClick={handleShare}
                className="tweet-action group"
              >
                <div className="p-2 rounded-full group-hover:bg-blue-50 dark:group-hover:bg-blue-900/20 transition-colors">
                  <ShareIcon className="w-5 h-5" />
                </div>
              </button>
            </div>
          </div>
        </div>
      </article>

      {/* Reply Composer Modal */}
      {showReplyComposer && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="modal-overlay" onClick={() => setShowReplyComposer(false)}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
              <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-dark-300">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  回复推文
                </h2>
                <button
                  onClick={() => setShowReplyComposer(false)}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-dark-300 rounded-full transition-colors"
                >
                  <svg className="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
              <div className="p-4">
                <TweetComposer
                  replyTo={displayTweet}
                  onSuccess={handleReplySuccess}
                  autoFocus
                  placeholder={`回复 @${displayTweet.author.username}`}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Quote Composer Modal */}
      {showQuoteComposer && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="modal-overlay" onClick={() => setShowQuoteComposer(false)}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
              <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-dark-300">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  引用推文
                </h2>
                <button
                  onClick={() => setShowQuoteComposer(false)}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-dark-300 rounded-full transition-colors"
                >
                  <svg className="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
              <div className="p-4">
                <TweetComposer
                  quoteTweet={displayTweet}
                  onSuccess={handleQuoteSuccess}
                  autoFocus
                  placeholder="添加评论"
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default TweetCard;