const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const TweetRetweet = sequelize.define('TweetRetweet', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  tweetId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'tweets',
      key: 'id'
    }
  },
  userId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  tableName: 'tweet_retweets',
  indexes: [
    {
      unique: true,
      fields: ['tweet_id', 'user_id']
    },
    {
      fields: ['tweet_id']
    },
    {
      fields: ['user_id']
    }
  ]
});

module.exports = TweetRetweet;