import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useSocket } from '../contexts/SocketContext';
import axios from 'axios';
import toast from 'react-hot-toast';
import InfiniteScroll from 'react-infinite-scroll-component';
import {
  ArrowLeftIcon,
  UserPlusIcon,
  UserMinusIcon
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import moment from 'moment';

function Followers() {
  const { username, type } = useParams(); // type: 'followers' or 'following'
  const navigate = useNavigate();
  const { user: currentUser } = useAuth();
  const { socket } = useSocket();
  const [user, setUser] = useState(null);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [followingUsers, setFollowingUsers] = useState(new Set());

  useEffect(() => {
    fetchUserAndList();
  }, [username, type]);

  useEffect(() => {
    if (currentUser) {
      // Initialize following status
      const following = new Set(currentUser.following || []);
      setFollowingUsers(following);
    }
  }, [currentUser]);

  useEffect(() => {
    if (socket) {
      socket.on('userFollowed', handleUserFollowed);
      socket.on('userUnfollowed', handleUserUnfollowed);

      return () => {
        socket.off('userFollowed', handleUserFollowed);
        socket.off('userUnfollowed', handleUserUnfollowed);
      };
    }
  }, [socket]);

  const fetchUserAndList = async () => {
    try {
      setLoading(true);
      setUsers([]);
      setPage(1);
      setHasMore(true);

      // Fetch user info
      const userResponse = await axios.get(`/api/users/${username}`);
      setUser(userResponse.data.user);

      // Fetch followers/following list
      const endpoint = type === 'followers' 
        ? `/api/users/${username}/followers`
        : `/api/users/${username}/following`;
      
      const response = await axios.get(`${endpoint}?page=1&limit=20`);
      setUsers(response.data.users);
      setHasMore(response.data.hasMore);
      setPage(2);
    } catch (error) {
      console.error('Error fetching data:', error);
      if (error.response?.status === 404) {
        toast.error('用户不存在');
        navigate('/');
      } else {
        toast.error('获取数据失败');
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchMoreUsers = async () => {
    try {
      const endpoint = type === 'followers' 
        ? `/api/users/${username}/followers`
        : `/api/users/${username}/following`;
      
      const response = await axios.get(`${endpoint}?page=${page}&limit=20`);
      setUsers(prev => [...prev, ...response.data.users]);
      setHasMore(response.data.hasMore);
      setPage(prev => prev + 1);
    } catch (error) {
      console.error('Error fetching more users:', error);
      toast.error('加载更多失败');
    }
  };

  const handleFollow = async (targetUser) => {
    if (!currentUser) {
      toast.error('请先登录');
      return;
    }

    if (targetUser._id === currentUser._id) {
      return;
    }

    try {
      const isFollowing = followingUsers.has(targetUser._id);
      
      if (isFollowing) {
        await axios.delete(`/api/users/${targetUser._id}/follow`);
        setFollowingUsers(prev => {
          const newSet = new Set(prev);
          newSet.delete(targetUser._id);
          return newSet;
        });
        toast.success(`已取消关注 ${targetUser.displayName}`);
      } else {
        await axios.post(`/api/users/${targetUser._id}/follow`);
        setFollowingUsers(prev => new Set([...prev, targetUser._id]));
        toast.success(`已关注 ${targetUser.displayName}`);
      }

      // Update user's follower count in the list
      setUsers(prev => prev.map(u => {
        if (u._id === targetUser._id) {
          return {
            ...u,
            followersCount: isFollowing ? u.followersCount - 1 : u.followersCount + 1
          };
        }
        return u;
      }));
    } catch (error) {
      console.error('Error following/unfollowing user:', error);
      toast.error('操作失败');
    }
  };

  const handleUserFollowed = (data) => {
    if (data.follower._id === currentUser?._id) {
      setFollowingUsers(prev => new Set([...prev, data.user._id]));
    }
    
    // Update follower count if the followed user is in the list
    setUsers(prev => prev.map(u => {
      if (u._id === data.user._id) {
        return { ...u, followersCount: u.followersCount + 1 };
      }
      return u;
    }));
  };

  const handleUserUnfollowed = (data) => {
    if (data.follower._id === currentUser?._id) {
      setFollowingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(data.user._id);
        return newSet;
      });
    }
    
    // Update follower count if the unfollowed user is in the list
    setUsers(prev => prev.map(u => {
      if (u._id === data.user._id) {
        return { ...u, followersCount: u.followersCount - 1 };
      }
      return u;
    }));
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-medium text-gray-900 dark:text-white mb-2">用户不存在</h2>
          <p className="text-gray-500 dark:text-gray-400">您访问的用户不存在或已被删除</p>
        </div>
      </div>
    );
  }

  const title = type === 'followers' ? '关注者' : '正在关注';
  const count = type === 'followers' ? user.followersCount : user.followingCount;

  return (
    <div className="min-h-screen bg-white dark:bg-dark-100">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white/80 dark:bg-dark-100/80 backdrop-blur-md border-b border-gray-200 dark:border-dark-300">
        <div className="max-w-2xl mx-auto px-4 py-3 flex items-center space-x-4">
          <button
            onClick={() => navigate(-1)}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-dark-200 rounded-full transition-colors"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">{title}</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              @{user.username} • {count} 人
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-2xl mx-auto">
        {users.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 dark:text-gray-500 mb-4">
              <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM9 9a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {type === 'followers' ? '还没有关注者' : '还没有关注任何人'}
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              {type === 'followers' 
                ? '当有人关注这个账户时，他们会出现在这里。'
                : '当这个账户关注其他人时，他们会出现在这里。'
              }
            </p>
          </div>
        ) : (
          <InfiniteScroll
            dataLength={users.length}
            next={fetchMoreUsers}
            hasMore={hasMore}
            loader={
              <div className="flex justify-center py-4">
                <LoadingSpinner size="sm" />
              </div>
            }
            endMessage={
              <div className="text-center py-4 text-gray-500 dark:text-gray-400 text-sm">
                没有更多用户了
              </div>
            }
          >
            <div className="divide-y divide-gray-200 dark:divide-dark-300">
              {users.map((targetUser) => {
                const isFollowing = followingUsers.has(targetUser._id);
                const isCurrentUser = currentUser?._id === targetUser._id;

                return (
                  <div key={targetUser._id} className="p-4 hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors">
                    <div className="flex items-start space-x-3">
                      {/* Avatar */}
                      <Link to={`/${targetUser.username}`}>
                        <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-200 dark:bg-dark-300 flex-shrink-0">
                          {targetUser.avatar ? (
                            <img
                              src={targetUser.avatar}
                              alt={targetUser.displayName}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500">
                              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                              </svg>
                            </div>
                          )}
                        </div>
                      </Link>

                      {/* User Info */}
                      <div className="flex-1 min-w-0">
                        <Link to={`/${targetUser.username}`} className="block">
                          <div className="flex items-center space-x-1">
                            <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                              {targetUser.displayName}
                            </h3>
                            {targetUser.verified && (
                              <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                              </svg>
                            )}
                          </div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">@{targetUser.username}</p>
                        </Link>
                        
                        {targetUser.bio && (
                          <p className="mt-1 text-sm text-gray-700 dark:text-gray-300 line-clamp-2">
                            {targetUser.bio}
                          </p>
                        )}
                        
                        <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                          <span>{targetUser.followersCount} 关注者</span>
                          <span>{targetUser.followingCount} 正在关注</span>
                          <span>加入于 {moment(targetUser.createdAt).format('YYYY年M月')}</span>
                        </div>
                      </div>

                      {/* Follow Button */}
                      {currentUser && !isCurrentUser && (
                        <button
                          onClick={() => handleFollow(targetUser)}
                          className={`flex items-center space-x-1 px-4 py-1.5 rounded-full text-sm font-medium transition-colors ${
                            isFollowing
                              ? 'bg-gray-200 dark:bg-dark-300 text-gray-900 dark:text-white hover:bg-red-100 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400'
                              : 'bg-blue-600 text-white hover:bg-blue-700'
                          }`}
                        >
                          {isFollowing ? (
                            <>
                              <UserMinusIcon className="h-4 w-4" />
                              <span className="hidden group-hover:inline">取消关注</span>
                              <span className="group-hover:hidden">已关注</span>
                            </>
                          ) : (
                            <>
                              <UserPlusIcon className="h-4 w-4" />
                              <span>关注</span>
                            </>
                          )}
                        </button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </InfiniteScroll>
        )}
      </div>
    </div>
  );
}

export default Followers;