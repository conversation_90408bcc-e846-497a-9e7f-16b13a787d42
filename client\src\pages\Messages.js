import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useSocket } from '../contexts/SocketContext';
import axios from 'axios';
import toast from 'react-hot-toast';
import moment from 'moment';
import {
  PaperAirplaneIcon,
  PhotoIcon,
  FaceSmileIcon,
  EllipsisHorizontalIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  TrashIcon,
  ArchiveBoxIcon
} from '@heroicons/react/24/outline';
import {
  CheckIcon,
  CheckCircleIcon
} from '@heroicons/react/24/solid';
import LoadingSpinner from '../components/UI/LoadingSpinner';

function Messages() {
  const { conversationId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { socket } = useSocket();
  const [conversations, setConversations] = useState([]);
  const [activeConversation, setActiveConversation] = useState(null);
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [messageText, setMessageText] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [showNewMessage, setShowNewMessage] = useState(false);
  const [searchUsers, setSearchUsers] = useState([]);
  const [searchingUsers, setSearchingUsers] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [showDropdown, setShowDropdown] = useState(false);
  const messagesEndRef = useRef(null);
  const fileInputRef = useRef(null);

  useEffect(() => {
    fetchConversations();
  }, []);

  useEffect(() => {
    if (conversationId) {
      fetchConversation(conversationId);
      fetchMessages(conversationId);
    }
  }, [conversationId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (socket) {
      // Join user's room for receiving messages
      socket.emit('join_user_room', user._id);

      // Listen for new messages
      socket.on('new_message', (message) => {
        if (message.conversation === conversationId) {
          setMessages(prev => [...prev, message]);
          markAsRead(message.conversation);
        }
        
        // Update conversation list
        setConversations(prev => prev.map(conv => 
          conv._id === message.conversation 
            ? { ...conv, lastMessage: message, unreadCount: conv._id === conversationId ? 0 : (conv.unreadCount || 0) + 1 }
            : conv
        ));
      });

      // Listen for message read status
      socket.on('message_read', (data) => {
        if (data.conversationId === conversationId) {
          setMessages(prev => prev.map(msg => 
            msg._id === data.messageId ? { ...msg, read: true } : msg
          ));
        }
      });

      // Listen for typing indicators
      socket.on('user_typing', (data) => {
        if (data.conversationId === conversationId && data.userId !== user._id) {
          // Handle typing indicator
          console.log('User is typing:', data);
        }
      });

      return () => {
        socket.off('new_message');
        socket.off('message_read');
        socket.off('user_typing');
      };
    }
  }, [socket, conversationId, user]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const fetchConversations = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/messages/conversations');
      setConversations(response.data.conversations);
    } catch (error) {
      console.error('Error fetching conversations:', error);
      toast.error('获取对话列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchConversation = async (id) => {
    try {
      const response = await axios.get(`/api/messages/conversations/${id}`);
      setActiveConversation(response.data.conversation);
    } catch (error) {
      console.error('Error fetching conversation:', error);
      toast.error('获取对话信息失败');
    }
  };

  const fetchMessages = async (id) => {
    try {
      setMessagesLoading(true);
      const response = await axios.get(`/api/messages/conversations/${id}/messages`);
      setMessages(response.data.messages);
      markAsRead(id);
    } catch (error) {
      console.error('Error fetching messages:', error);
      toast.error('获取消息失败');
    } finally {
      setMessagesLoading(false);
    }
  };

  const markAsRead = async (conversationId) => {
    try {
      await axios.put(`/api/messages/conversations/${conversationId}/read`);
      setConversations(prev => prev.map(conv => 
        conv._id === conversationId ? { ...conv, unreadCount: 0 } : conv
      ));
      
      // Dispatch custom event to update unread count in other components
      window.dispatchEvent(new CustomEvent('messagesRead'));
    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  };

  const sendMessage = async (e) => {
    e.preventDefault();
    if (!messageText.trim() || sending) return;

    try {
      setSending(true);
      const response = await axios.post(`/api/messages/conversations/${conversationId}/messages`, {
        content: messageText.trim()
      });
      
      setMessages(prev => [...prev, response.data.message]);
      setMessageText('');
      
      // Update conversation in list
      setConversations(prev => prev.map(conv => 
        conv._id === conversationId 
          ? { ...conv, lastMessage: response.data.message }
          : conv
      ));
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('发送消息失败');
    } finally {
      setSending(false);
    }
  };

  const searchUsersForNewMessage = async (query) => {
    if (!query.trim()) {
      setSearchUsers([]);
      return;
    }

    try {
      setSearchingUsers(true);
      const response = await axios.get('/api/users/search', {
        params: { q: query, limit: 10 }
      });
      setSearchUsers(response.data.users.filter(u => u._id !== user._id));
    } catch (error) {
      console.error('Error searching users:', error);
    } finally {
      setSearchingUsers(false);
    }
  };

  const startNewConversation = async (targetUser) => {
    try {
      const response = await axios.post('/api/messages/conversations', {
        participantId: targetUser._id
      });
      
      const newConversation = response.data.conversation;
      setConversations(prev => [newConversation, ...prev]);
      navigate(`/messages/${newConversation._id}`);
      setShowNewMessage(false);
      setSelectedUser(null);
      setSearchQuery('');
      setSearchUsers([]);
    } catch (error) {
      console.error('Error starting conversation:', error);
      toast.error('创建对话失败');
    }
  };

  const deleteConversation = async (id) => {
    try {
      await axios.delete(`/api/messages/conversations/${id}`);
      setConversations(prev => prev.filter(conv => conv._id !== id));
      if (conversationId === id) {
        navigate('/messages');
      }
      toast.success('对话已删除');
      setShowDropdown(false);
    } catch (error) {
      console.error('Error deleting conversation:', error);
      toast.error('删除对话失败');
    }
  };

  const archiveConversation = async (id) => {
    try {
      await axios.put(`/api/messages/conversations/${id}/archive`);
      setConversations(prev => prev.filter(conv => conv._id !== id));
      if (conversationId === id) {
        navigate('/messages');
      }
      toast.success('对话已归档');
      setShowDropdown(false);
    } catch (error) {
      console.error('Error archiving conversation:', error);
      toast.error('归档对话失败');
    }
  };

  const getOtherParticipant = (conversation) => {
    return conversation.participants.find(p => p._id !== user._id);
  };

  const formatMessageTime = (timestamp) => {
    const now = moment();
    const messageTime = moment(timestamp);
    
    if (now.diff(messageTime, 'days') === 0) {
      return messageTime.format('HH:mm');
    } else if (now.diff(messageTime, 'days') === 1) {
      return '昨天 ' + messageTime.format('HH:mm');
    } else if (now.diff(messageTime, 'weeks') === 0) {
      return messageTime.format('dddd HH:mm');
    } else {
      return messageTime.format('MM/DD HH:mm');
    }
  };

  const filteredConversations = conversations.filter(conv => {
    const otherUser = getOtherParticipant(conv);
    return otherUser?.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
           otherUser?.username.toLowerCase().includes(searchQuery.toLowerCase());
  });

  return (
    <div className="h-screen bg-white dark:bg-dark-100 flex">
      {/* Conversations Sidebar */}
      <div className="w-80 border-r border-gray-200 dark:border-dark-300 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-dark-300">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">消息</h1>
            <button
              onClick={() => setShowNewMessage(true)}
              className="p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors"
            >
              <PlusIcon className="h-5 w-5" />
            </button>
          </div>
          
          {/* Search */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="搜索对话"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-dark-300 rounded-lg bg-white dark:bg-dark-200 text-gray-900 dark:text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Conversations List */}
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner size="md" />
            </div>
          ) : filteredConversations.length === 0 ? (
            <div className="text-center py-8 px-4">
              <p className="text-gray-500 dark:text-gray-400">
                {searchQuery ? '没有找到匹配的对话' : '还没有任何对话'}
              </p>
              {!searchQuery && (
                <button
                  onClick={() => setShowNewMessage(true)}
                  className="mt-2 text-blue-500 hover:text-blue-600 text-sm"
                >
                  开始新对话
                </button>
              )}
            </div>
          ) : (
            filteredConversations.map((conversation) => {
              const otherUser = getOtherParticipant(conversation);
              const isActive = conversationId === conversation._id;
              
              return (
                <div
                  key={conversation._id}
                  onClick={() => navigate(`/messages/${conversation._id}`)}
                  className={`p-4 border-b border-gray-200 dark:border-dark-300 cursor-pointer hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors ${
                    isActive ? 'bg-blue-50 dark:bg-blue-900/20 border-r-2 border-r-blue-500' : ''
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    {/* Avatar */}
                    <div className="relative flex-shrink-0">
                      {otherUser?.avatar ? (
                        <img
                          src={otherUser.avatar}
                          alt={otherUser.displayName}
                          className="w-12 h-12 rounded-full"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-300 dark:bg-dark-300 rounded-full flex items-center justify-center">
                          <span className="text-lg font-medium text-gray-600 dark:text-gray-400">
                            {otherUser?.displayName?.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      )}
                      {otherUser?.isOnline && (
                        <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-dark-100" />
                      )}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {otherUser?.displayName}
                        </h3>
                        <div className="flex items-center space-x-1">
                          {conversation.lastMessage && (
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {formatMessageTime(conversation.lastMessage.createdAt)}
                            </span>
                          )}
                          {conversation.unreadCount > 0 && (
                            <span className="bg-blue-500 text-white text-xs font-medium px-2 py-1 rounded-full min-w-[20px] text-center">
                              {conversation.unreadCount > 99 ? '99+' : conversation.unreadCount}
                            </span>
                          )}
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                        @{otherUser?.username}
                      </p>
                      {conversation.lastMessage && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 truncate mt-1">
                          {conversation.lastMessage.sender === user._id ? '您: ' : ''}
                          {conversation.lastMessage.content}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 flex flex-col">
        {conversationId && activeConversation ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-gray-200 dark:border-dark-300 bg-white dark:bg-dark-100">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {(() => {
                    const otherUser = getOtherParticipant(activeConversation);
                    return (
                      <>
                        <div className="relative">
                          {otherUser?.avatar ? (
                            <img
                              src={otherUser.avatar}
                              alt={otherUser.displayName}
                              className="w-10 h-10 rounded-full"
                            />
                          ) : (
                            <div className="w-10 h-10 bg-gray-300 dark:bg-dark-300 rounded-full flex items-center justify-center">
                              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                                {otherUser?.displayName?.charAt(0).toUpperCase()}
                              </span>
                            </div>
                          )}
                          {otherUser?.isOnline && (
                            <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-dark-100" />
                          )}
                        </div>
                        <div>
                          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                            {otherUser?.displayName}
                          </h2>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            @{otherUser?.username}
                            {otherUser?.isOnline && <span className="ml-2">在线</span>}
                          </p>
                        </div>
                      </>
                    );
                  })()}
                </div>
                
                <div className="relative">
                  <button
                    onClick={() => setShowDropdown(!showDropdown)}
                    className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-dark-200 transition-colors"
                  >
                    <EllipsisHorizontalIcon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                  </button>
                  {showDropdown && (
                    <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-dark-200 rounded-lg shadow-lg border border-gray-200 dark:border-dark-300 py-1 z-20">
                      <button
                        onClick={() => archiveConversation(conversationId)}
                        className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-300 flex items-center space-x-2"
                      >
                        <ArchiveBoxIcon className="h-4 w-4" />
                        <span>归档对话</span>
                      </button>
                      <button
                        onClick={() => deleteConversation(conversationId)}
                        className="w-full px-4 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-dark-300 flex items-center space-x-2"
                      >
                        <TrashIcon className="h-4 w-4" />
                        <span>删除对话</span>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messagesLoading ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner size="md" />
                </div>
              ) : messages.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500 dark:text-gray-400">
                    还没有消息，开始对话吧！
                  </p>
                </div>
              ) : (
                messages.map((message, index) => {
                  const isOwn = message.sender === user._id;
                  const showTime = index === 0 || 
                    moment(message.createdAt).diff(moment(messages[index - 1].createdAt), 'minutes') > 5;
                  
                  return (
                    <div key={message._id}>
                      {showTime && (
                        <div className="text-center mb-4">
                          <span className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-dark-200 px-3 py-1 rounded-full">
                            {formatMessageTime(message.createdAt)}
                          </span>
                        </div>
                      )}
                      <div className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}>
                        <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                          isOwn 
                            ? 'bg-blue-500 text-white' 
                            : 'bg-gray-200 dark:bg-dark-200 text-gray-900 dark:text-white'
                        }`}>
                          <p className="text-sm">{message.content}</p>
                          <div className={`flex items-center justify-end mt-1 space-x-1 ${
                            isOwn ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'
                          }`}>
                            <span className="text-xs">
                              {moment(message.createdAt).format('HH:mm')}
                            </span>
                            {isOwn && (
                              message.read ? (
                                <CheckCircleIcon className="h-3 w-3" />
                              ) : (
                                <CheckIcon className="h-3 w-3" />
                              )
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Message Input */}
            <div className="p-4 border-t border-gray-200 dark:border-dark-300 bg-white dark:bg-dark-100">
              <form onSubmit={sendMessage} className="flex items-end space-x-3">
                <div className="flex-1">
                  <div className="relative">
                    <textarea
                      value={messageText}
                      onChange={(e) => setMessageText(e.target.value)}
                      placeholder="输入消息..."
                      rows={1}
                      className="block w-full px-4 py-3 pr-12 border border-gray-300 dark:border-dark-300 rounded-lg bg-white dark:bg-dark-200 text-gray-900 dark:text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                      style={{ minHeight: '44px', maxHeight: '120px' }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          sendMessage(e);
                        }
                      }}
                    />
                    <div className="absolute right-2 bottom-2 flex items-center space-x-1">
                      <button
                        type="button"
                        onClick={() => fileInputRef.current?.click()}
                        className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                      >
                        <PhotoIcon className="h-5 w-5" />
                      </button>
                      <button
                        type="button"
                        className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                      >
                        <FaceSmileIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
                <button
                  type="submit"
                  disabled={!messageText.trim() || sending}
                  className="p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {sending ? (
                    <LoadingSpinner size="sm" color="white" />
                  ) : (
                    <PaperAirplaneIcon className="h-5 w-5" />
                  )}
                </button>
              </form>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                className="hidden"
                onChange={(e) => {
                  // Handle file upload
                  console.log('File selected:', e.target.files[0]);
                }}
              />
            </div>
          </>
        ) : (
          /* No Conversation Selected */
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-200 dark:bg-dark-200 rounded-full flex items-center justify-center mx-auto mb-4">
                <PaperAirplaneIcon className="h-8 w-8 text-gray-400" />
              </div>
              <h2 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
                选择一个对话
              </h2>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                从左侧选择一个对话开始聊天
              </p>
              <button
                onClick={() => setShowNewMessage(true)}
                className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                开始新对话
              </button>
            </div>
          </div>
        )}
      </div>

      {/* New Message Modal */}
      {showNewMessage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-dark-200 rounded-lg w-full max-w-md max-h-[80vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-dark-300">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">新消息</h3>
              <button
                onClick={() => {
                  setShowNewMessage(false);
                  setSelectedUser(null);
                  setSearchQuery('');
                  setSearchUsers([]);
                }}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-4">
              {/* Search Input */}
              <div className="relative mb-4">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="搜索用户"
                  value={searchQuery}
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                    searchUsersForNewMessage(e.target.value);
                  }}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-dark-300 rounded-lg bg-white dark:bg-dark-200 text-gray-900 dark:text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Search Results */}
              <div className="max-h-60 overflow-y-auto">
                {searchingUsers ? (
                  <div className="flex justify-center py-4">
                    <LoadingSpinner size="sm" />
                  </div>
                ) : searchUsers.length === 0 && searchQuery ? (
                  <div className="text-center py-4">
                    <p className="text-gray-500 dark:text-gray-400">没有找到用户</p>
                  </div>
                ) : (
                  searchUsers.map((searchUser) => (
                    <div
                      key={searchUser._id}
                      onClick={() => startNewConversation(searchUser)}
                      className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-300 cursor-pointer transition-colors"
                    >
                      {searchUser.avatar ? (
                        <img
                          src={searchUser.avatar}
                          alt={searchUser.displayName}
                          className="w-10 h-10 rounded-full"
                        />
                      ) : (
                        <div className="w-10 h-10 bg-gray-300 dark:bg-dark-300 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            {searchUser.displayName.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {searchUser.displayName}
                        </h4>
                        <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                          @{searchUser.username}
                        </p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Messages;