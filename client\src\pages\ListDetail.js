import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useSocket } from '../contexts/SocketContext';
import axios from 'axios';
import toast from 'react-hot-toast';
import InfiniteScroll from 'react-infinite-scroll-component';
import {
  ArrowLeftIcon,
  ListBulletIcon,
  UserGroupIcon,
  LockClosedIcon,
  GlobeAltIcon,
  EllipsisHorizontalIcon,
  PencilIcon,
  TrashIcon,
  UserPlusIcon,
  UserMinusIcon,
  ShareIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import TweetCard from '../components/Tweet/TweetCard';
import UserCard from '../components/User/UserCard';
import moment from 'moment';

function ListDetail() {
  const { listId } = useParams();
  const { user } = useAuth();
  const { socket } = useSocket();
  const navigate = useNavigate();
  
  const [list, setList] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('tweets'); // 'tweets' or 'members'
  const [tweets, setTweets] = useState([]);
  const [members, setMembers] = useState([]);
  const [tweetsPage, setTweetsPage] = useState(1);
  const [membersPage, setMembersPage] = useState(1);
  const [hasMoreTweets, setHasMoreTweets] = useState(true);
  const [hasMoreMembers, setHasMoreMembers] = useState(true);
  const [tweetsLoading, setTweetsLoading] = useState(false);
  const [membersLoading, setMembersLoading] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showAddMemberModal, setShowAddMemberModal] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [subscribing, setSubscribing] = useState(false);
  
  // Add member form
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [searching, setSearching] = useState(false);
  const [addingMembers, setAddingMembers] = useState(new Set());

  useEffect(() => {
    if (listId) {
      fetchList();
    }
  }, [listId]);

  useEffect(() => {
    if (list && activeTab === 'tweets' && tweets.length === 0) {
      fetchTweets();
    } else if (list && activeTab === 'members' && members.length === 0) {
      fetchMembers();
    }
  }, [list, activeTab]);

  useEffect(() => {
    if (socket) {
      socket.on('tweetDeleted', handleTweetDeleted);
      socket.on('tweetUpdated', handleTweetUpdated);
      socket.on('listUpdated', handleListUpdated);
      socket.on('listDeleted', handleListDeleted);
      
      return () => {
        socket.off('tweetDeleted', handleTweetDeleted);
        socket.off('tweetUpdated', handleTweetUpdated);
        socket.off('listUpdated', handleListUpdated);
        socket.off('listDeleted', handleListDeleted);
      };
    }
  }, [socket]);

  const fetchList = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/lists/${listId}`);
      setList(response.data.list);
    } catch (error) {
      console.error('Error fetching list:', error);
      if (error.response?.status === 404) {
        toast.error('列表不存在');
        navigate('/lists');
      } else if (error.response?.status === 403) {
        toast.error('您没有权限查看此列表');
        navigate('/lists');
      } else {
        toast.error('获取列表失败');
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchTweets = async (page = 1) => {
    if (tweetsLoading) return;
    
    try {
      setTweetsLoading(true);
      const response = await axios.get(`/api/lists/${listId}/tweets`, {
        params: { page, limit: 10 }
      });
      
      const newTweets = response.data.tweets;
      if (page === 1) {
        setTweets(newTweets);
      } else {
        setTweets(prev => [...prev, ...newTweets]);
      }
      
      setHasMoreTweets(newTweets.length === 10);
      setTweetsPage(page + 1);
    } catch (error) {
      console.error('Error fetching tweets:', error);
      toast.error('获取推文失败');
    } finally {
      setTweetsLoading(false);
    }
  };

  const fetchMembers = async (page = 1) => {
    if (membersLoading) return;
    
    try {
      setMembersLoading(true);
      const response = await axios.get(`/api/lists/${listId}/members`, {
        params: { page, limit: 20 }
      });
      
      const newMembers = response.data.members;
      if (page === 1) {
        setMembers(newMembers);
      } else {
        setMembers(prev => [...prev, ...newMembers]);
      }
      
      setHasMoreMembers(newMembers.length === 20);
      setMembersPage(page + 1);
    } catch (error) {
      console.error('Error fetching members:', error);
      toast.error('获取成员失败');
    } finally {
      setMembersLoading(false);
    }
  };

  const searchUsers = async (query) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }
    
    try {
      setSearching(true);
      const response = await axios.get('/api/users/search', {
        params: { q: query, limit: 10 }
      });
      setSearchResults(response.data.users);
    } catch (error) {
      console.error('Error searching users:', error);
      toast.error('搜索用户失败');
    } finally {
      setSearching(false);
    }
  };

  const handleSubscribe = async () => {
    try {
      setSubscribing(true);
      if (list.isSubscribed) {
        await axios.delete(`/api/lists/${listId}/subscribe`);
        setList(prev => ({
          ...prev,
          isSubscribed: false,
          subscribersCount: prev.subscribersCount - 1
        }));
        toast.success('已取消关注列表');
      } else {
        await axios.post(`/api/lists/${listId}/subscribe`);
        setList(prev => ({
          ...prev,
          isSubscribed: true,
          subscribersCount: prev.subscribersCount + 1
        }));
        toast.success('已关注列表');
      }
    } catch (error) {
      console.error('Error subscribing to list:', error);
      toast.error(list.isSubscribed ? '取消关注失败' : '关注失败');
    } finally {
      setSubscribing(false);
    }
  };

  const handleDeleteList = async () => {
    try {
      setDeleting(true);
      await axios.delete(`/api/lists/${listId}`);
      toast.success('列表已删除');
      navigate('/lists');
    } catch (error) {
      console.error('Error deleting list:', error);
      toast.error('删除列表失败');
    } finally {
      setDeleting(false);
    }
  };

  const handleAddMember = async (userId) => {
    try {
      setAddingMembers(prev => new Set([...prev, userId]));
      await axios.post(`/api/lists/${listId}/members`, { userId });
      
      // Update members list if on members tab
      if (activeTab === 'members') {
        const userToAdd = searchResults.find(u => u._id === userId);
        if (userToAdd) {
          setMembers(prev => [userToAdd, ...prev]);
        }
      }
      
      // Update list member count
      setList(prev => ({
        ...prev,
        membersCount: prev.membersCount + 1
      }));
      
      toast.success('已添加到列表');
    } catch (error) {
      console.error('Error adding member:', error);
      toast.error('添加成员失败');
    } finally {
      setAddingMembers(prev => {
        const newSet = new Set(prev);
        newSet.delete(userId);
        return newSet;
      });
    }
  };

  const handleRemoveMember = async (userId) => {
    try {
      await axios.delete(`/api/lists/${listId}/members/${userId}`);
      setMembers(prev => prev.filter(member => member._id !== userId));
      setList(prev => ({
        ...prev,
        membersCount: prev.membersCount - 1
      }));
      toast.success('已从列表中移除');
    } catch (error) {
      console.error('Error removing member:', error);
      toast.error('移除成员失败');
    }
  };

  const handleTweetDeleted = (tweetId) => {
    setTweets(prev => prev.filter(tweet => tweet._id !== tweetId));
  };

  const handleTweetUpdated = (updatedTweet) => {
    setTweets(prev => prev.map(tweet => 
      tweet._id === updatedTweet._id ? updatedTweet : tweet
    ));
  };

  const handleListUpdated = (updatedList) => {
    if (updatedList._id === listId) {
      setList(updatedList);
    }
  };

  const handleListDeleted = (deletedListId) => {
    if (deletedListId === listId) {
      toast.error('列表已被删除');
      navigate('/lists');
    }
  };

  const handleShareList = async () => {
    try {
      const url = `${window.location.origin}/lists/${listId}`;
      await navigator.clipboard.writeText(url);
      toast.success('链接已复制到剪贴板');
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      toast.error('复制链接失败');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!list) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            列表不存在
          </h2>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            您要查找的列表可能已被删除或您没有权限访问。
          </p>
          <Link
            to="/lists"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
          >
            返回列表页面
          </Link>
        </div>
      </div>
    );
  }

  const isOwner = user && list.owner._id === user._id;
  const canManage = isOwner;

  return (
    <div className="min-h-screen bg-white dark:bg-dark-100">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white/80 dark:bg-dark-100/80 backdrop-blur-md border-b border-gray-200 dark:border-dark-300">
        <div className="max-w-2xl mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => navigate(-1)}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-dark-300 rounded-full transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5" />
            </button>
            <div>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                {list.name}
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {list.membersCount} 成员
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {!isOwner && (
              <button
                onClick={handleSubscribe}
                disabled={subscribing}
                className={`px-4 py-1.5 text-sm font-medium rounded-lg transition-colors ${
                  list.isSubscribed
                    ? 'bg-gray-200 dark:bg-dark-300 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-dark-400'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                } disabled:opacity-50`}
              >
                {subscribing ? (
                  <LoadingSpinner size="sm" color={list.isSubscribed ? 'gray' : 'white'} />
                ) : (
                  list.isSubscribed ? '已关注' : '关注'
                )}
              </button>
            )}
            
            <div className="relative">
              <button
                onClick={() => setDropdownOpen(!dropdownOpen)}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-300 rounded-full transition-colors"
              >
                <EllipsisHorizontalIcon className="h-5 w-5" />
              </button>
              
              {dropdownOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-dark-200 rounded-lg shadow-lg border border-gray-200 dark:border-dark-300 py-1 z-10">
                  <button
                    onClick={() => {
                      handleShareList();
                      setDropdownOpen(false);
                    }}
                    className="flex items-center space-x-2 w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-300 transition-colors"
                  >
                    <ShareIcon className="h-4 w-4" />
                    <span>分享列表</span>
                  </button>
                  
                  {canManage && (
                    <>
                      <Link
                        to={`/lists/${listId}/edit`}
                        className="flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-300 transition-colors"
                        onClick={() => setDropdownOpen(false)}
                      >
                        <PencilIcon className="h-4 w-4" />
                        <span>编辑列表</span>
                      </Link>
                      <button
                        onClick={() => {
                          setShowDeleteModal(true);
                          setDropdownOpen(false);
                        }}
                        className="flex items-center space-x-2 w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                      >
                        <TrashIcon className="h-4 w-4" />
                        <span>删除列表</span>
                      </button>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* List Info */}
      <div className="max-w-2xl mx-auto p-4 border-b border-gray-200 dark:border-dark-300">
        <div className="flex items-start space-x-4">
          <div className="w-16 h-16 rounded-lg bg-gray-200 dark:bg-dark-300 flex items-center justify-center flex-shrink-0">
            <ListBulletIcon className="h-8 w-8 text-gray-600 dark:text-gray-400" />
          </div>
          
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                {list.name}
              </h2>
              {list.isPrivate ? (
                <LockClosedIcon className="h-5 w-5 text-gray-400" />
              ) : (
                <GlobeAltIcon className="h-5 w-5 text-gray-400" />
              )}
            </div>
            
            {list.description && (
              <p className="text-gray-700 dark:text-gray-300 mb-3">
                {list.description}
              </p>
            )}
            
            <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-3">
              <span className="flex items-center space-x-1">
                <UserGroupIcon className="h-4 w-4" />
                <span>{list.membersCount} 成员</span>
              </span>
              <span>{list.subscribersCount} 关注者</span>
            </div>
            
            <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
              <span>由</span>
              <Link
                to={`/profile/${list.owner.username}`}
                className="font-medium text-blue-600 dark:text-blue-400 hover:underline"
              >
                @{list.owner.username}
              </Link>
              <span>创建于 {moment(list.createdAt).format('YYYY年M月D日')}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="max-w-2xl mx-auto">
        <div className="flex border-b border-gray-200 dark:border-dark-300">
          <button
            onClick={() => setActiveTab('tweets')}
            className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'tweets'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            推文
          </button>
          <button
            onClick={() => setActiveTab('members')}
            className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'members'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            成员 ({list.membersCount})
            {canManage && (
              <button
                onClick={() => setShowAddMemberModal(true)}
                className="ml-2 p-1 text-blue-600 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded-full transition-colors"
              >
                <PlusIcon className="h-4 w-4" />
              </button>
            )}
          </button>
        </div>

        {/* Content */}
        {activeTab === 'tweets' ? (
          <div>
            {tweets.length === 0 && !tweetsLoading ? (
              <div className="text-center py-12">
                <div className="text-gray-400 dark:text-gray-500 mb-4">
                  <ListBulletIcon className="mx-auto h-12 w-12" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  暂无推文
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  此列表的成员还没有发布推文。
                </p>
              </div>
            ) : (
              <InfiniteScroll
                dataLength={tweets.length}
                next={() => fetchTweets(tweetsPage)}
                hasMore={hasMoreTweets}
                loader={
                  <div className="flex justify-center py-4">
                    <LoadingSpinner size="md" />
                  </div>
                }
                endMessage={
                  tweets.length > 0 && (
                    <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                      没有更多推文了
                    </div>
                  )
                }
              >
                <div className="divide-y divide-gray-200 dark:divide-dark-300">
                  {tweets.map((tweet) => (
                    <TweetCard
                      key={tweet._id}
                      tweet={tweet}
                      onUpdate={(updatedTweet) => handleTweetUpdated(updatedTweet)}
                      onDelete={() => handleTweetDeleted(tweet._id)}
                    />
                  ))}
                </div>
              </InfiniteScroll>
            )}
          </div>
        ) : (
          <div>
            {members.length === 0 && !membersLoading ? (
              <div className="text-center py-12">
                <div className="text-gray-400 dark:text-gray-500 mb-4">
                  <UserGroupIcon className="mx-auto h-12 w-12" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  暂无成员
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  此列表还没有添加任何成员。
                </p>
                {canManage && (
                  <button
                    onClick={() => setShowAddMemberModal(true)}
                    className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <UserPlusIcon className="h-4 w-4" />
                    <span>添加成员</span>
                  </button>
                )}
              </div>
            ) : (
              <InfiniteScroll
                dataLength={members.length}
                next={() => fetchMembers(membersPage)}
                hasMore={hasMoreMembers}
                loader={
                  <div className="flex justify-center py-4">
                    <LoadingSpinner size="md" />
                  </div>
                }
                endMessage={
                  members.length > 0 && (
                    <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                      没有更多成员了
                    </div>
                  )
                }
              >
                <div className="divide-y divide-gray-200 dark:divide-dark-300">
                  {members.map((member) => (
                    <div key={member._id} className="p-4 flex items-center justify-between">
                      <UserCard user={member} />
                      {canManage && (
                        <button
                          onClick={() => handleRemoveMember(member._id)}
                          className="p-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-full transition-colors"
                          title="从列表中移除"
                        >
                          <UserMinusIcon className="h-5 w-5" />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              </InfiniteScroll>
            )}
          </div>
        )}
      </div>

      {/* Add Member Modal */}
      {showAddMemberModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-dark-200 rounded-lg w-full max-w-md max-h-[80vh] flex flex-col">
            <div className="p-6 border-b border-gray-200 dark:border-dark-300">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                添加成员到列表
              </h3>
              <div className="relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                    searchUsers(e.target.value);
                  }}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-dark-300 rounded-lg bg-white dark:bg-dark-200 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="搜索用户名或显示名称"
                />
                {searching && (
                  <div className="absolute right-3 top-2.5">
                    <LoadingSpinner size="sm" />
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex-1 overflow-y-auto">
              {searchResults.length === 0 ? (
                <div className="p-6 text-center text-gray-500 dark:text-gray-400">
                  {searchQuery ? '未找到用户' : '输入用户名开始搜索'}
                </div>
              ) : (
                <div className="divide-y divide-gray-200 dark:divide-dark-300">
                  {searchResults.map((searchUser) => {
                    const isAlreadyMember = members.some(member => member._id === searchUser._id);
                    const isAdding = addingMembers.has(searchUser._id);
                    
                    return (
                      <div key={searchUser._id} className="p-4 flex items-center justify-between">
                        <UserCard user={searchUser} />
                        <button
                          onClick={() => handleAddMember(searchUser._id)}
                          disabled={isAlreadyMember || isAdding}
                          className={`px-3 py-1.5 text-sm font-medium rounded-lg transition-colors ${
                            isAlreadyMember
                              ? 'bg-gray-200 dark:bg-dark-300 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                              : 'bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50'
                          }`}
                        >
                          {isAdding ? (
                            <LoadingSpinner size="sm" color="white" />
                          ) : isAlreadyMember ? (
                            '已添加'
                          ) : (
                            '添加'
                          )}
                        </button>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
            
            <div className="p-6 border-t border-gray-200 dark:border-dark-300">
              <button
                onClick={() => {
                  setShowAddMemberModal(false);
                  setSearchQuery('');
                  setSearchResults([]);
                }}
                className="w-full px-4 py-2 border border-gray-300 dark:border-dark-300 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-200 hover:bg-gray-50 dark:hover:bg-dark-300 transition-colors"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete List Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-dark-200 rounded-lg w-full max-w-md">
            <div className="p-6">
              <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 dark:bg-red-900/20 rounded-full mb-4">
                <TrashIcon className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white text-center mb-2">
                删除列表
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 text-center mb-6">
                确定要删除列表 "{list.name}" 吗？此操作无法撤销。
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowDeleteModal(false)}
                  disabled={deleting}
                  className="flex-1 px-4 py-2 border border-gray-300 dark:border-dark-300 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-200 hover:bg-gray-50 dark:hover:bg-dark-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={handleDeleteList}
                  disabled={deleting}
                  className="flex-1 px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {deleting ? <LoadingSpinner size="sm" color="white" /> : '删除列表'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default ListDetail;