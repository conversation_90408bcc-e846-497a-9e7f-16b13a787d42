import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';
import toast from 'react-hot-toast';
import {
  PlusIcon,
  ListBulletIcon,
  UserGroupIcon,
  LockClosedIcon,
  GlobeAltIcon,
  EllipsisHorizontalIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import moment from 'moment';

function Lists() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('owned'); // 'owned' or 'subscribed'
  const [ownedLists, setOwnedLists] = useState([]);
  const [subscribedLists, setSubscribedLists] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedList, setSelectedList] = useState(null);
  const [dropdownOpen, setDropdownOpen] = useState(null);
  
  // Create list form
  const [createForm, setCreateForm] = useState({
    name: '',
    description: '',
    isPrivate: false
  });
  const [creating, setCreating] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    if (user) {
      fetchLists();
    }
  }, [user]);

  const fetchLists = async () => {
    try {
      setLoading(true);
      const [ownedResponse, subscribedResponse] = await Promise.all([
        axios.get('/api/lists/owned'),
        axios.get('/api/lists/subscribed')
      ]);
      
      setOwnedLists(ownedResponse.data.lists);
      setSubscribedLists(subscribedResponse.data.lists);
    } catch (error) {
      console.error('Error fetching lists:', error);
      toast.error('获取列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateList = async (e) => {
    e.preventDefault();
    
    if (!createForm.name.trim()) {
      toast.error('请输入列表名称');
      return;
    }
    
    try {
      setCreating(true);
      const response = await axios.post('/api/lists', createForm);
      setOwnedLists(prev => [response.data.list, ...prev]);
      setCreateForm({ name: '', description: '', isPrivate: false });
      setShowCreateModal(false);
      toast.success('列表创建成功');
    } catch (error) {
      console.error('Error creating list:', error);
      toast.error('创建列表失败');
    } finally {
      setCreating(false);
    }
  };

  const handleDeleteList = async () => {
    if (!selectedList) return;
    
    try {
      setDeleting(true);
      await axios.delete(`/api/lists/${selectedList._id}`);
      setOwnedLists(prev => prev.filter(list => list._id !== selectedList._id));
      setShowDeleteModal(false);
      setSelectedList(null);
      toast.success('列表已删除');
    } catch (error) {
      console.error('Error deleting list:', error);
      toast.error('删除列表失败');
    } finally {
      setDeleting(false);
    }
  };

  const handleUnsubscribe = async (listId) => {
    try {
      await axios.delete(`/api/lists/${listId}/subscribe`);
      setSubscribedLists(prev => prev.filter(list => list._id !== listId));
      toast.success('已取消关注列表');
    } catch (error) {
      console.error('Error unsubscribing from list:', error);
      toast.error('取消关注失败');
    }
  };

  const currentLists = activeTab === 'owned' ? ownedLists : subscribedLists;

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white dark:bg-dark-100">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white/80 dark:bg-dark-100/80 backdrop-blur-md border-b border-gray-200 dark:border-dark-300">
        <div className="max-w-2xl mx-auto px-4 py-3 flex items-center justify-between">
          <h1 className="text-xl font-bold text-gray-900 dark:text-white">列表</h1>
          <button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center space-x-1 px-3 py-1.5 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
          >
            <PlusIcon className="h-4 w-4" />
            <span>新建列表</span>
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="max-w-2xl mx-auto">
        <div className="flex border-b border-gray-200 dark:border-dark-300">
          <button
            onClick={() => setActiveTab('owned')}
            className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'owned'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            您的列表 ({ownedLists.length})
          </button>
          <button
            onClick={() => setActiveTab('subscribed')}
            className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'subscribed'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            关注的列表 ({subscribedLists.length})
          </button>
        </div>

        {/* Content */}
        {currentLists.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 dark:text-gray-500 mb-4">
              <ListBulletIcon className="mx-auto h-12 w-12" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {activeTab === 'owned' ? '您还没有创建任何列表' : '您还没有关注任何列表'}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 max-w-sm mx-auto mb-4">
              {activeTab === 'owned' 
                ? '创建列表来整理您关注的账户，并查看他们的推文。'
                : '关注其他用户创建的列表来发现新内容。'
              }
            </p>
            {activeTab === 'owned' && (
              <button
                onClick={() => setShowCreateModal(true)}
                className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
              >
                <PlusIcon className="h-4 w-4" />
                <span>创建列表</span>
              </button>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-dark-300">
            {currentLists.map((list) => (
              <div key={list._id} className="p-4 hover:bg-gray-50 dark:hover:bg-dark-200 transition-colors">
                <div className="flex items-start space-x-3">
                  {/* List Icon */}
                  <div className="w-12 h-12 rounded-lg bg-gray-200 dark:bg-dark-300 flex items-center justify-center flex-shrink-0">
                    <ListBulletIcon className="h-6 w-6 text-gray-600 dark:text-gray-400" />
                  </div>

                  {/* List Info */}
                  <div className="flex-1 min-w-0">
                    <Link to={`/lists/${list._id}`} className="block">
                      <div className="flex items-center space-x-2">
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {list.name}
                        </h3>
                        {list.isPrivate ? (
                          <LockClosedIcon className="h-4 w-4 text-gray-400" />
                        ) : (
                          <GlobeAltIcon className="h-4 w-4 text-gray-400" />
                        )}
                      </div>
                      {list.description && (
                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                          {list.description}
                        </p>
                      )}
                      <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                        <span className="flex items-center space-x-1">
                          <UserGroupIcon className="h-3 w-3" />
                          <span>{list.membersCount} 成员</span>
                        </span>
                        <span>{list.subscribersCount} 关注者</span>
                        <span>创建于 {moment(list.createdAt).format('YYYY年M月D日')}</span>
                      </div>
                    </Link>
                  </div>

                  {/* Actions */}
                  <div className="relative">
                    <button
                      onClick={() => setDropdownOpen(dropdownOpen === list._id ? null : list._id)}
                      className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-300 rounded-full transition-colors"
                    >
                      <EllipsisHorizontalIcon className="h-5 w-5" />
                    </button>
                    
                    {dropdownOpen === list._id && (
                      <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-dark-200 rounded-lg shadow-lg border border-gray-200 dark:border-dark-300 py-1 z-10">
                        {activeTab === 'owned' ? (
                          <>
                            <Link
                              to={`/lists/${list._id}/edit`}
                              className="flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-300 transition-colors"
                              onClick={() => setDropdownOpen(null)}
                            >
                              <PencilIcon className="h-4 w-4" />
                              <span>编辑列表</span>
                            </Link>
                            <button
                              onClick={() => {
                                setSelectedList(list);
                                setShowDeleteModal(true);
                                setDropdownOpen(null);
                              }}
                              className="flex items-center space-x-2 w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                            >
                              <TrashIcon className="h-4 w-4" />
                              <span>删除列表</span>
                            </button>
                          </>
                        ) : (
                          <button
                            onClick={() => {
                              handleUnsubscribe(list._id);
                              setDropdownOpen(null);
                            }}
                            className="flex items-center space-x-2 w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                          >
                            <TrashIcon className="h-4 w-4" />
                            <span>取消关注</span>
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Create List Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-dark-200 rounded-lg w-full max-w-md">
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                创建新列表
              </h3>
              <form onSubmit={handleCreateList} className="space-y-4">
                <div>
                  <label htmlFor="listName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    列表名称
                  </label>
                  <input
                    type="text"
                    id="listName"
                    value={createForm.name}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, name: e.target.value }))}
                    className="block w-full px-3 py-2 border border-gray-300 dark:border-dark-300 rounded-lg bg-white dark:bg-dark-200 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="输入列表名称"
                    maxLength={25}
                    disabled={creating}
                  />
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    {createForm.name.length}/25
                  </p>
                </div>
                
                <div>
                  <label htmlFor="listDescription" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    描述（可选）
                  </label>
                  <textarea
                    id="listDescription"
                    rows={3}
                    value={createForm.description}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, description: e.target.value }))}
                    className="block w-full px-3 py-2 border border-gray-300 dark:border-dark-300 rounded-lg bg-white dark:bg-dark-200 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    placeholder="描述这个列表的用途"
                    maxLength={100}
                    disabled={creating}
                  />
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    {createForm.description.length}/100
                  </p>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isPrivate"
                    checked={createForm.isPrivate}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, isPrivate: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-dark-300 rounded"
                    disabled={creating}
                  />
                  <label htmlFor="isPrivate" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    设为私密列表
                  </label>
                </div>
                
                <div className="flex space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => {
                      setShowCreateModal(false);
                      setCreateForm({ name: '', description: '', isPrivate: false });
                    }}
                    disabled={creating}
                    className="flex-1 px-4 py-2 border border-gray-300 dark:border-dark-300 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-200 hover:bg-gray-50 dark:hover:bg-dark-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={creating || !createForm.name.trim()}
                    className="flex-1 flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {creating ? <LoadingSpinner size="sm" color="white" /> : '创建列表'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Delete List Modal */}
      {showDeleteModal && selectedList && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-dark-200 rounded-lg w-full max-w-md">
            <div className="p-6">
              <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 dark:bg-red-900/20 rounded-full mb-4">
                <TrashIcon className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white text-center mb-2">
                删除列表
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 text-center mb-6">
                确定要删除列表 "{selectedList.name}" 吗？此操作无法撤销。
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => {
                    setShowDeleteModal(false);
                    setSelectedList(null);
                  }}
                  disabled={deleting}
                  className="flex-1 px-4 py-2 border border-gray-300 dark:border-dark-300 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-200 hover:bg-gray-50 dark:hover:bg-dark-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={handleDeleteList}
                  disabled={deleting}
                  className="flex-1 px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {deleting ? <LoadingSpinner size="sm" color="white" /> : '删除列表'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Lists;