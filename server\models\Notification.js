const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Notification = sequelize.define('Notification', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  recipientId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  senderId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  type: {
    type: DataTypes.ENUM('like', 'retweet', 'reply', 'mention', 'follow', 'quote', 'welcome'),
    allowNull: false
  },
  message: {
    type: DataTypes.STRING,
    allowNull: false
  },
  tweetId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'tweets',
      key: 'id'
    }
  },
  isRead: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  isDeleted: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  metadata: {
    type: DataTypes.JSONB,
    defaultValue: {}
  }
}, {
  tableName: 'notifications',
  indexes: [
    {
      fields: ['recipient_id', 'created_at']
    },
    {
      fields: ['recipient_id', 'is_read']
    },
    {
      fields: ['sender_id']
    },
    {
      fields: ['type']
    },
    {
      fields: ['is_deleted']
    }
  ]
});

// Static method to create notification
Notification.createNotification = async function(data) {
  const { recipientId, senderId, type, tweetId, metadata = {} } = data;
  
  // Don't create notification if sender is the same as recipient
  if (senderId === recipientId) {
    return null;
  }
  
  // Generate message based on type
  let message = '';
  switch (type) {
    case 'like':
      message = 'liked your tweet';
      break;
    case 'retweet':
      message = 'retweeted your tweet';
      break;
    case 'reply':
      message = 'replied to your tweet';
      break;
    case 'mention':
      message = 'mentioned you in a tweet';
      break;
    case 'follow':
      message = 'started following you';
      break;
    case 'quote':
      message = 'quoted your tweet';
      break;
    case 'welcome':
      message = 'Welcome to Twitter Clone!';
      break;
    default:
      message = 'interacted with your content';
  }
  
  // Check if similar notification already exists (to avoid spam)
  if (type !== 'welcome') {
    const existingNotification = await this.findOne({
      where: {
        recipientId,
        senderId,
        type,
        tweetId,
        createdAt: {
          [sequelize.Sequelize.Op.gte]: new Date(Date.now() - 60 * 60 * 1000) // Within last hour
        }
      }
    });
    
    if (existingNotification) {
      // Update timestamp instead of creating new notification
      existingNotification.createdAt = new Date();
      existingNotification.isRead = false;
      return await existingNotification.save();
    }
  }
  
  const notification = await this.create({
    recipientId,
    senderId,
    type,
    message,
    tweetId,
    metadata
  });
  
  return notification;
};

// Static method to mark notifications as read
Notification.markAsRead = async function(recipientId, notificationIds = []) {
  const where = { recipientId, isRead: false };
  
  if (notificationIds.length > 0) {
    where.id = { [sequelize.Sequelize.Op.in]: notificationIds };
  }
  
  return await this.update({ isRead: true }, { where });
};

// Static method to get unread count
Notification.getUnreadCount = async function(recipientId) {
  return await this.count({
    where: {
      recipientId,
      isRead: false,
      isDeleted: false
    }
  });
};

// Static method to clean old notifications
Notification.cleanOldNotifications = async function(daysOld = 30) {
  const cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);
  
  return await this.destroy({
    where: {
      createdAt: { [sequelize.Sequelize.Op.lt]: cutoffDate },
      isRead: true
    }
  });
};

// Instance method to mark as read
Notification.prototype.markAsRead = async function() {
  this.isRead = true;
  return await this.save();
};

// Instance method to soft delete
Notification.prototype.softDelete = async function() {
  this.isDeleted = true;
  return await this.save();
};

// Virtual for time ago
Notification.prototype.getTimeAgo = function() {
  const now = new Date();
  const diff = now - this.createdAt;
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);
  
  if (minutes < 1) return 'just now';
  if (minutes < 60) return `${minutes}m`;
  if (hours < 24) return `${hours}h`;
  if (days < 7) return `${days}d`;
  return this.createdAt.toLocaleDateString();
};

module.exports = Notification;